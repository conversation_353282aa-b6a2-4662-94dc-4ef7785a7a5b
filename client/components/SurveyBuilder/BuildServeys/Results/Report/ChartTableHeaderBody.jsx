'use client';
import { FaPhoneAlt } from 'react-icons/fa';
import { LuUser2 } from 'react-icons/lu';
import { MdImage, MdOutlineEmail } from 'react-icons/md';

export function generateMatrixVisualizationData(userResponse) {
  const { answer, questionTypeData } = userResponse;
  const matrix = questionTypeData?.matrix || [];

  if (!matrix.length) {
    console.error('Matrix data is empty or invalid');
    return [];
  }

  // Extract categories from the first row of the matrix
  const categories = matrix[0].slice(1);

  // Initialize categoryCounts dynamically based on matrix data
  const categoryCounts = {};
  categories.forEach((category) => {
    categoryCounts[category] = {};
    matrix.slice(1).forEach((row) => {
      const item = row[0];
      if (item) {
        categoryCounts[category][item] = 0;
      }
    });
  });

  // Process answers to count occurrences per category
  answer.forEach((row) => {
    const responses = row.split(',');
    responses.forEach((response, rowIndex) => {
      const categoryIndex = parseInt(response) - 1; // Convert response to zero-based index
      const category = categories[categoryIndex];
      const item = matrix[rowIndex + 1]?.[0]; // Safely access the item name
      if (category && item && categoryCounts[category]?.[item] !== undefined) {
        categoryCounts[category][item] += 1;
      }
    });
  });

  // Calculate totals and percentages
  const totals = {};
  categories.forEach((category) => {
    totals[category] = Object.values(categoryCounts[category]).reduce(
      (a, b) => a + b,
      0
    );
  });

  const totalResponses = answer.length;

  // Format data for visualization
  const formattedData = matrix.slice(1).map((row) => {
    const item = row[0];
    const result = { item };
    categories.forEach((category) => {
      const count = categoryCounts[category][item] || 0;
      result[category] = {
        count,
        percentage: totalResponses
          ? ((count / totalResponses) * 100).toFixed(2) + '%'
          : '0.00%',
      };
    });
    return result;
  });
  // Calculate percentages for each category (total row)
  const totalPercentages = {};
  const grandTotalCount = Object.values(totals).reduce((a, b) => a + b, 0);

  categories.forEach((category) => {
    totalPercentages[category] = grandTotalCount
      ? ((totals[category] / grandTotalCount) * 100).toFixed(2) + '%'
      : '0.00%';
  });
  // Add total row to the formatted data
  formattedData.push({
    item: 'Total',
    ...categories.reduce((acc, category) => {
      acc[category] = {
        count: totals[category],
        percentage: totalPercentages[category],
      };
      return acc;
    }, {}),
  });

  return formattedData;
}

export function generateRankingVisualizationData(userResponse) {
  const { answer, questionTypeData } = userResponse;
  const { factors = [], total_factors } = questionTypeData || {};

  if (!factors.length || total_factors <= 0) {
    console.error('Ranking data is empty or invalid');
    return [];
  }

  // Initialize rankCounts and totalScores for each factor
  const rankCounts = {};
  const totalScores = {};

  factors.forEach(({ factor }) => {
    rankCounts[factor] = Array(total_factors).fill(0); // Initialize rank counts for each rank
    totalScores[factor] = 0; // Initialize total score
  });

  // Process user answers to count ranks and calculate scores
  answer.forEach((rankedString) => {
    const ranks = rankedString.split(',').map(Number); // Convert ranks to numbers

    ranks.forEach((rank, index) => {
      const factor = factors[index]?.factor; // Get the factor for this index
      if (factor && rank > 0 && rank <= total_factors) {
        rankCounts[factor][rank - 1] += 1; // Increment rank count (zero-based index)
        totalScores[factor] += rank; // Add the rank to total score
      }
    });
  });

  const totalResponses = answer.length;

  // Format data for visualization
  const formattedData = factors.map(({ factor }) => {
    const rankData = rankCounts[factor].map((count, rankIndex) => ({
      rank: rankIndex + 1,
      count,
      percentage: totalResponses
        ? ((count / totalResponses) * 100).toFixed(2) + '%'
        : '0.00%',
    }));

    return {
      factor,
      rankData,
      totalScore: totalScores[factor], // Total Score for the factor
      averageRank: totalResponses
        ? (totalScores[factor] / totalResponses).toFixed(2)
        : '0.00',
    };
  });

  return { formattedData };
}

export const generateChartTableHeader = (questionWithAnswers) => {
  const targetQuestionType = [
    'multiple_choice',
    'Scale',
    'Rating',
    'picture_choice',
    'Boolean',
    'Slider',
    'Number',
    // 'constant_sum',
  ];
  if (targetQuestionType.includes(questionWithAnswers?.questionType)) {
    return (
      <thead className="text-base font-semibold text-gray-700 uppercase  dark:bg-gray-700 dark:text-gray-400">
        <tr>
          <th scope="col" className="px-6 py-3">
            Answer
          </th>
          <th scope="col" className="px-6 py-3">
            Response
          </th>
          <th scope="col" className="px-6 py-3">
            Response Percentage
          </th>
        </tr>
      </thead>
    );
  }
  if (
    questionWithAnswers?.questionType === 'Text' ||
    questionWithAnswers?.questionType === 'open_list'
  ) {
    return (
      <thead className="text-base font-semibold text-gray-700 uppercase ">
        <tr>
          <th scope="col" className="px-6 py-3">
            Words
          </th>
          <th scope="col" className="px-6 py-3">
            No.of Response
          </th>
        </tr>
      </thead>
    );
  }
  if (
    questionWithAnswers?.questionType === 'Email' ||
    questionWithAnswers?.questionType === 'Phone_Number' ||
    questionWithAnswers?.questionType === 'upload'
  ) {
    return (
      <thead className="text-base font-semibold text-gray-700 uppercase ">
        <tr>
          <th scope="col" className="px-6 py-3">
            Responded
          </th>
          <th scope="col" className="px-6 py-3">
            Response
          </th>
          <th scope="col" className="px-6 py-3">
            No.of Response
          </th>
        </tr>
      </thead>
    );
  }
  if (questionWithAnswers?.questionType === 'contact_form') {
    return (
      <thead className="text-base font-semibold text-gray-700 uppercase ">
        <tr>
          {questionWithAnswers?.questionTypeData?.fields?.map((item, index) => (
            <th key={index} scope="col" className="px-6 py-3">
              <div className="flex gap-2 items-center">
                <LuUser2 />
                {item?.label}
              </div>
            </th>
          ))}
        </tr>
      </thead>
    );
  }
  if (
    questionWithAnswers?.questionType === 'matrix' ||
    questionWithAnswers?.questionType === 'MatrixSlider'
  ) {
    return (
      <thead className="text-base font-semibold text-gray-700 uppercase ">
        <tr>
          {questionWithAnswers?.questionTypeData?.matrix[0]?.map(
            (row, index) => (
              <th key={index} scope="col" className="px-6 py-3">
                {row}
              </th>
            )
          )}
        </tr>
      </thead>
    );
  }
  if (questionWithAnswers?.questionType === 'Ranking') {
    const { range, factors } = questionWithAnswers?.questionTypeData || {};

    return (
      <thead className="text-base font-semibold text-gray-700 uppercase">
        <tr>
          {/* Header for Factors */}
          <th scope="col" className="px-6 py-3">
            Factor
          </th>

          {/* Headers for Ranks */}
          {Array.from({ length: range }, (_, index) => (
            <th key={index} scope="col" className="px-6 py-3">
              Rank {index + 1}
            </th>
          ))}

          {/* Header for Score */}
          <th scope="col" className="px-6 py-3">
            Score
          </th>

          {/* Header for Average Rank */}
          <th scope="col" className="px-6 py-3">
            Average Rank
          </th>
        </tr>
      </thead>
    );
  }
  if (questionWithAnswers?.questionType === 'constant_sum') {
    return (
      <thead className="text-base font-semibold text-gray-700 uppercase">
        <tr>
          <th scope="col" className="px-6 py-3">
            Statement
          </th>
          <th scope="col" className="px-6 py-3">
            Average value
          </th>
          <th scope="col" className="px-6 py-3">
            Total value
          </th>
        </tr>
      </thead>
    );
  }
};

export const generateChartTableBody = (questionWithAnswers) => {
  const targetQuestionType = [
    'multiple_choice',
    'Scale',
    'Rating',
    'picture_choice',
    'Boolean',
    'Slider',
    'Number',
  ];
  const generateAnswerSummary = (answerList) => {
    // if (targetQuestionType.includes(questionWithAnswers?.questionType)) {

    // }
    // Count occurrences of each answer
    const answers = answerList
      ?.flatMap((item) => item?.split(','))
      .filter((a) => a.trim() !== '');
    const answerCounts = answers?.reduce((acc, answer) => {
      answer = answer?.trim()?.toLowerCase(); // Trim whitespace and standardize case
      acc[answer] = (acc[answer] || 0) + 1;
      return acc;
    }, {});

    const totalResponses = answers.length;

    // Generate the summary array
    return Object.keys(answerCounts).map((answer) => ({
      answer: answer.charAt(0).toUpperCase() + answer.slice(1), // Capitalize first letter
      responses: answerCounts[answer],
      percentage:
        ((answerCounts[answer] / totalResponses) * 100).toFixed(2) + '%',
    }));
  };

  console.log({ questionWithAnswers }, 'from table');

  if (targetQuestionType.includes(questionWithAnswers?.questionType)) {
    return (
      <tbody>
        {generateAnswerSummary(questionWithAnswers.answer).map(
          (item, index) => (
            <tr
              key={index}
              className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
            >
              <td className="px-6 py-4">
                <div>{item.answer}</div>
              </td>

              <td className="px-6 py-4">{item.responses}</td>
              <td className="px-6 py-4">{item.percentage}</td>
            </tr>
          )
        )}
        {Array.isArray(questionWithAnswers.otherAnswerArray) &&
        questionWithAnswers.otherAnswerArray.some(
          (item) => typeof item === 'string' && item.trim() !== undefined
        ) &&
        questionWithAnswers?.questionTypeData?.allow_other_option ? (
          <div className="pt-3 px-6">
            <strong className="">Other Answer-</strong>
            {Array.isArray(questionWithAnswers.otherAnswerArray) &&
              (() => {
                const uniqueStrings = [
                  ...new Set(
                    questionWithAnswers.otherAnswerArray.filter(
                      (val) => typeof val === 'string'
                    )
                  ),
                ];
                return uniqueStrings.length > 0 ? (
                  <div className="flex items-center gap-2 mt-1 text-sm text-gray-600 flex-wrap">
                    {uniqueStrings.map((val, idx) => (
                      <span key={idx} className="bg-gray-200 px-2 py-1 rounded">
                        {val}
                      </span>
                    ))}
                  </div>
                ) : null;
              })()}
          </div>
        ) : null}
      </tbody>
    );
  }
  if (
    questionWithAnswers?.questionType === 'Text' ||
    questionWithAnswers?.questionType === 'open_list'
  ) {
    return (
      <tbody>
        {generateAnswerSummary(questionWithAnswers.answer).map(
          (item, index) => (
            <tr
              key={index}
              className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
            >
              <td className="px-6 py-4">{item.answer}</td>
              <td className="px-6 py-4">{item.responses}</td>
            </tr>
          )
        )}
      </tbody>
    );
  }

  if (
    questionWithAnswers?.questionType === 'Email' ||
    questionWithAnswers?.questionType === 'Phone_Number' ||
    questionWithAnswers?.questionType === 'upload'
  ) {
    return (
      <tbody>
        {generateAnswerSummary(questionWithAnswers.answer).map(
          (item, index) => {
            if (questionWithAnswers?.questionType === 'upload') {
              return (
                <tr
                  key={index}
                  className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
                >
                  <td className="px-6 py-4">Anonymous</td>
                  <td className="px-6 py-4 flex gap-2 items-center">
                    <MdImage />

                    {(() => {
                      try {
                        const parsedAnswer = JSON.parse(item.answer);
                        return parsedAnswer[0]?.image || '---';
                      } catch (e) {
                        // If parsing fails, fallback to the raw string or default
                        return item.answer || '---';
                      }
                    })()}
                  </td>
                  <td className="px-6 py-4">{item.responses}</td>
                </tr>
              );
            }
            return (
              <tr
                key={index}
                className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
              >
                <td className="px-6 py-4">Anonymous</td>
                <td className="px-6 py-4 flex gap-2 items-center">
                  {questionWithAnswers?.questionType === 'Phone_Number' ? (
                    <FaPhoneAlt />
                  ) : (
                    <MdOutlineEmail />
                  )}

                  {item.answer}
                </td>
                <td className="px-6 py-4">{item.responses}</td>
              </tr>
            );
          }
        )}
      </tbody>
    );
  }

  if (questionWithAnswers?.questionType === 'contact_form') {
    // questionWithAnswers.answer.map((item) => console.log(item));

    const retriveValidAnswerForTable = questionWithAnswers.answer.map(
      (item) => {
        if (item !== '--skipped--' && item.length > 100) {
          return item;
        } else {
          return '{}';
        }
      }
    );
    // console.log(retriveValidAnswerForTable);

    const parsedAnswers = retriveValidAnswerForTable
      .map((answer) => JSON.parse(answer))
      .filter((item) => Object.keys(item).length > 0);

    let tableHeaders = [];

    if (
      Array.isArray(parsedAnswers) &&
      parsedAnswers.length > 0 &&
      parsedAnswers[0]
    ) {
      tableHeaders = Object.keys(parsedAnswers[0]);
    }

    // let tableHeaders = Object.keys(parsedAnswers[0]);

    return (
      <tbody>
        {parsedAnswers
          .filter((item) => Object.keys(item).length > 0)
          .map((item, index) => (
            <tr
              key={index}
              className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
            >
              {tableHeaders.map((td, index) => (
                <td key={index} className="px-6 py-4">
                  {item[td] || '---'}
                </td>
              ))}
            </tr>
          ))}
      </tbody>
    );
  }

  if (
    questionWithAnswers?.questionType === 'matrix' ||
    questionWithAnswers?.questionType === 'MatrixSlider'
  ) {
    return (
      <tbody>
        {generateMatrixVisualizationData(questionWithAnswers).map(
          (item, index) => (
            <tr
              key={index}
              className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
            >
              {/* Item name */}
              <td className="px-6 py-4">{item.item}</td>

              {/* Categories (e.g., Good, Bad) */}
              {Object.keys(item)
                .filter((key) => key !== 'item') // Exclude the item name
                .map((category) => (
                  <td key={category} className="px-6 py-4">
                    {item[category].count} ({item[category].percentage})
                  </td>
                ))}
            </tr>
          )
        )}
      </tbody>
    );
  }

  if (questionWithAnswers?.questionType === 'Ranking') {
    const { formattedData } =
      generateRankingVisualizationData(questionWithAnswers);

    return (
      <tbody>
        {formattedData.map((item, index) => (
          <tr
            key={index}
            className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
          >
            {/* Factor name */}
            <td className="px-6 py-4">{item.factor}</td>

            {/* Ranks and Percentages */}
            {item.rankData.map((rank, rankIndex) => (
              <td key={rankIndex} className="px-6 py-4">
                {rank.count} ({rank.percentage})
              </td>
            ))}

            {/* Total Score */}
            <td className="px-6 py-4 font-semibold">{item.totalScore}</td>

            {/* Average Rank */}
            <td className="px-6 py-4 font-semibold">Avg: {item.averageRank}</td>
          </tr>
        ))}
      </tbody>
    );
  }

  if (questionWithAnswers?.questionType === 'constant_sum') {
    const options = questionWithAnswers?.questionTypeData?.options || [];
    const answers = questionWithAnswers?.answer || [];

    // Calculate total sum of all answers
    const totalSum = answers.reduce((sum, value) => {
      const num = parseFloat(value.split(',')[0]); // Extract numeric value before comma
      return sum + (isNaN(num) ? 0 : num);
    }, 0);

    // Calculate average for each option
    const averageValues = options.map((option, index) => {
      const values = answers.map((ans) =>
        parseFloat(ans.split(',')[index] || 0)
      );
      const sum = values.reduce((acc, val) => acc + val, 0);
      const avg = values.length > 0 ? sum / values.length : 0;
      return { option, avg: avg.toFixed(2), total: sum };
    });

    return (
      <tbody>
        {averageValues.map((item, index) => (
          <tr
            key={index}
            className="bg-[#f5f5f5] border-[#d9d9d9] border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 last:border-0"
          >
            <td className="px-6 py-4">{item.option}</td>
            <td className="px-6 py-4">{item.avg}</td>
            <td className="px-6 py-4">{item.total}</td>
          </tr>
        ))}
      </tbody>
    );
  }
};
