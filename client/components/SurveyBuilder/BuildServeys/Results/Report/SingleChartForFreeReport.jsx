'use client';
import { F<PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON><PERSON>, FaChart<PERSON>ie } from 'react-icons/fa';

import React, { memo, useEffect, useRef, useState } from 'react';
import {
  generateChartTableBody,
  generateChartTableHeader,
} from './ChartTableHeaderBody';
import CommonQuestionTypeChart from './CustomQuestionChart/CommonQuestionTypeChart';
import ConstantSumTypeChart from './CustomQuestionChart/ConstantSumTypeChart';
import HorizontalBarChartForMatrix from './CustomQuestionChart/HorizontalBarChartForMatrix';
import VerticalBarChartForRanking from './CustomQuestionChart/VerticalBarChartForRanking';

const SingleChartForFreeReport = React.forwardRef(
  ({ index, questionWithAnswers, respondents, totalQuestion }, ref) => {
    const [chartType, setChartType] = useState('vertical-bars');
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);

    // <PERSON>le clicks outside the dropdown to close it
    useEffect(() => {
      function handleClickOutside(event) {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target)
        ) {
          setIsOpen(false);
        }
      }

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);
    return (
      <>
        <div
          style={{
            maxWidth: '1220px',
            fontSize: '24px',
          }}
          id="print"
          className="relative -z-10"
        ></div>
        {/* for showing in the ui  */}
        <div className="grid grid-cols-1 #grid-cols-[1fr_0.08fr] ">
          <div>
            {/* Top Side Banner  */}
            <div className="bg-primary text-white tracking-wider p-10 rounded-tl-2xl rounded-tr-2xl">
              <div className="flex gap-2 uppercase text-base">
                <span className="font-semibold text-sm">
                  Question {index < 10 && '0'}
                  {index + 1}
                </span>
                <span>
                  {questionWithAnswers?.questionType === 'multiple_choice'
                    ? 'Multiple Choice'
                    : questionWithAnswers?.questionType === 'Phone_Number'
                    ? 'Phone Number'
                    : questionWithAnswers?.questionType === 'contact_form'
                    ? 'Contact Form'
                    : questionWithAnswers?.questionType === 'open_list'
                    ? 'Open List'
                    : questionWithAnswers?.questionType === 'picture_choice'
                    ? 'Picture Choice'
                    : questionWithAnswers?.questionType === 'constant_sum'
                    ? 'Constant Sum'
                    : questionWithAnswers?.questionType === 'MatrixSlider'
                    ? 'Matrix Slider'
                    : questionWithAnswers?.questionType === 'Phone_Number'
                    ? 'Phone Number'
                    : questionWithAnswers?.questionType}
                </span>
              </div>
              <div className="flex items-center">
                <p
                  className="font-light text-xl"
                  dangerouslySetInnerHTML={{
                    __html: questionWithAnswers?.question || '',
                  }}
                />
              </div>
              <div className="mt-8 flex gap-3 font-medium">
                <span>
                  Answered:{' '}
                  <b>
                    {
                      questionWithAnswers?.answer?.filter(
                        (item) => item !== '--not answered--'
                      ).length
                    }
                  </b>
                </span>
                <span>
                  Skipped:{' '}
                  <b>
                    {
                      questionWithAnswers?.answer?.filter(
                        (item) => item === '--not answered--'
                      ).length
                    }
                  </b>
                </span>
              </div>
            </div>

            {/* Chart Start */}
            <div className="custom__shadow rounded-bl-2xl rounded-br-2xl bg-white p-10 relative">
              {/* Chart type change pop up */}
              <div
                id="ignorePDF"
                className={`w-[40%] flex justify-end items-center gap-3  z-10 absolute top-2 right-8 `}
              >
                <>
                  {![
                    'Email',
                    'Phone_Number',
                    'Number',
                    'Slider',
                    'upload',
                    'contact_form',
                    'Text',
                    'open_list',
                  ].includes(questionWithAnswers?.questionType) && (
                    <div
                      style={{ marginRight: '-65px' }}
                      className="relative"
                      ref={dropdownRef}
                    >
                      <div className="relative inline-block text-left">
                        <button
                          onClick={() => setIsOpen(!isOpen)}
                          className="flex items-center justify-center p-2 text-sm rounded-md bg-primary text-white shadow-sm hover:bg-secondary focus:outline-none focus:ring-2 "
                        >
                          {chartType === 'vertical-bars' && (
                            <FaChartBar title="Vertical Bar" />
                          )}
                          {chartType === 'horizontal-bars' && (
                            <FaChartBar
                              className="rotate-90"
                              title="Horizontal Bar"
                            />
                          )}
                          {chartType === 'pie' && (
                            <FaChartPie title="Pie chart" />
                          )}
                          {chartType === 'radar' && (
                            <FaChartArea title="Line chart" />
                          )}
                        </button>
                        {isOpen &&
                          !['matrix', 'MatrixSlider', 'Ranking'].includes(
                            questionWithAnswers?.questionType
                          ) && (
                            <ul
                              className="absolute right-0 z-10 mt-2 w-10 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                              role="menu"
                              aria-orientation="vertical"
                              aria-labelledby="chart-type-menu"
                            >
                              <li
                                title="Vertical Bar"
                                className="hover:bg-gray-100 rounded-t-md"
                              >
                                <a
                                  onClick={() => {
                                    setChartType('vertical-bars');
                                    setIsOpen(false);
                                  }}
                                  className="flex justify-center items-center p-2 cursor-pointer"
                                >
                                  <FaChartBar />
                                </a>
                              </li>
                              <li
                                title="Horizontal Bar"
                                className="hover:bg-gray-100"
                              >
                                <a
                                  onClick={() => {
                                    setChartType('horizontal-bars');
                                    setIsOpen(false);
                                  }}
                                  className="flex justify-center items-center p-2 cursor-pointer"
                                >
                                  <FaChartBar className="rotate-90" />
                                </a>
                              </li>
                              <li
                                title="Pie chart"
                                className="hover:bg-gray-100"
                              >
                                <a
                                  onClick={() => {
                                    setChartType('pie');
                                    setIsOpen(false);
                                  }}
                                  className="flex justify-center items-center p-2 cursor-pointer"
                                >
                                  <FaChartPie />
                                </a>
                              </li>
                              <li
                                title="Line chart"
                                className="hover:bg-gray-100 rounded-b-md"
                              >
                                <a
                                  onClick={() => {
                                    setChartType('radar');
                                    setIsOpen(false);
                                  }}
                                  className="flex justify-center items-center p-2 cursor-pointer"
                                >
                                  <FaChartArea />
                                </a>
                              </li>
                            </ul>
                          )}
                      </div>
                    </div>
                  )}
                </>
              </div>
              {/* Chart Body start */}
              <div className="">
                <>
                  {/* Chart Show */}
                  <div>
                    <div className="relative w-full">
                      <div>
                        <>
                          {questionWithAnswers?.questionType === 'Ranking' && (
                            <VerticalBarChartForRanking
                              userResponse={questionWithAnswers}
                            />
                          )}
                          {['matrix', 'MatrixSlider'].includes(
                            questionWithAnswers?.questionType
                          ) && (
                            <HorizontalBarChartForMatrix
                              userResponse={questionWithAnswers}
                            />
                          )}
                          {[
                            'multiple_choice',
                            'Scale',
                            'picture_choice',
                            'Boolean',
                            'Rating',
                          ].includes(questionWithAnswers?.questionType) && (
                            <CommonQuestionTypeChart
                              chartType={chartType}
                              userResponse={questionWithAnswers}
                            />
                          )}
                          {questionWithAnswers?.questionType ===
                            'constant_sum' && (
                            <ConstantSumTypeChart
                              chartType={chartType}
                              userResponse={questionWithAnswers}
                            />
                          )}
                        </>
                      </div>
                    </div>
                  </div>

                  {/* Chart Data Table */}
                  <div>
                    <div className="relative overflow-x-auto shadow-md sm:rounded-lg bg-[#f5f5f5] p-[20px]">
                      <table className="w-full text-base text-left rtl:text-right text-gray-700 dark:text-gray-400 bg-[#f5f5f5] ">
                        {generateChartTableHeader(questionWithAnswers)}
                        {generateChartTableBody(questionWithAnswers)}
                      </table>
                    </div>
                  </div>
                </>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
);

SingleChartForFreeReport.displayName = 'SingleChartForFreeReport';
export default memo(SingleChartForFreeReport);
