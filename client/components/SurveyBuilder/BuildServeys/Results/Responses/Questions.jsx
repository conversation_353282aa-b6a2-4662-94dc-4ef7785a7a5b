'use client';
import { AiFillStar, AiTwotoneCrown } from 'react-icons/ai';
import { BiSolidLike } from 'react-icons/bi';

const MultipleChoice = ({ question, answer, otherAnswer }) => {
  // Use the same delimiter as in the main component
  const DELIMITER = '|||';

  // Split by the special delimiter instead of comma
  const choices = answer?.split(DELIMITER) || [];

  const isOtherAnswer = answer === 'Other';

  return (
    <div className="mt-2 flex flex-wrap gap-5">
      {choices?.map((choice, index) => (
        <div
          key={index}
          className="flex justify-between items-center bg-[#F3F8F6] w-52 p-2 border border-slate-400 rounded-md cursor-pointer"
        >
          <span className="capitalize">
            {isOtherAnswer ? <>{`Other: ${otherAnswer}`}</> : <>{choice}</>}
          </span>
        </div>
      ))}
    </div>
  );
};
const OpenList = ({ answer }) => {
  // Use the same delimiter as in the main component
  const DELIMITER = ',';
  // Split by the special delimiter instead of comma
  const choices =
    answer?.split(DELIMITER).filter((ans) => ans.trim() !== '') || [];

  return (
    <div className="mt-2 flex flex-wrap gap-5">
      {choices?.map((choice, index) => (
        <div
          key={index}
          className="flex justify-between items-center bg-[#F3F8F6] w-52 p-2 border border-slate-400 rounded-md cursor-pointer"
        >
          <span className="capitalize">{choice}</span>
        </div>
      ))}
    </div>
  );
};

const Rating = ({ answer, question }) => {
  // console.log(answer, 'answerra');
  return (
    <div className="mt-2 mb-2 flex gap-6 text-gray-400">
      {Array(answer * 1)
        .fill(answer * 1)
        .map((number, index) => (
          <div className="text-center" key={index}>
            {question?.questionTypeData?.icon === 'AiOutlineCrown' ? (
              <AiTwotoneCrown className=" text-gray-400" size="50px" />
            ) : question?.questionTypeData?.icon === 'BiLike' ? (
              <BiSolidLike className="text-gray-400" size="50px" />
            ) : (
              <AiFillStar className="text-gray-400" size="50px" />
            )}
          </div>
        ))}
    </div>
  );
};
const ContactForm = ({ answer, question }) => {
  const form = JSON.parse(answer);
  const fields = question?.questionTypeData?.fields;
  return (
    <div className="mt-8 flex flex-col gap-5">
      {fields?.map(
        (field, index) =>
          form[field.label] && (
            <div
              key={index}
              className={`flex justify-between items-center w-[400px]  p-2 border border-slate-400`}
            >
              <span className="capitalize">{field.label}</span>
              <span className="capitalize">{form[field.label]}</span>
            </div>
          )
      )}
    </div>
  );
};
const ConstantSum = ({ answer, question }) => {
  const form = answer?.split(',');
  const fields = question?.questionTypeData?.options;
  const total = form?.reduce(
    (accumulator, currentValue) => accumulator + currentValue * 1,
    0
  );
  // console.log(total, fields, 'totaltotaltotal');
  return (
    <div className="mt-8 flex flex-col gap-5">
      <span>
        Total:{' '}
        {question?.questionTypeData?.total_to
          ? question?.questionTypeData?.total_to
          : total}
      </span>
      {fields?.map((field, index) => (
        <div
          key={index}
          className={`flex justify-between items-center w-[400px]  p-2 border border-slate-400`}
        >
          <span className="capitalize">{field}</span>
          <span className="capitalize">{form[index]}</span>
        </div>
      ))}
    </div>
  );
};
const PictureChoice = ({ answer, question }) => {
  const choices = answer?.split(',');
  const qChoices = question.questionTypeData?.choices;
  // console.log(
  //   question.questionTypeData?.choices,
  //   choices,
  //   'questioeenquestion'
  // );
  return (
    <div className="mt-8 flex flex-wrap gap-5">
      {choices?.map((choice, index) =>
        qChoices?.map(
          (qchoice, key) =>
            qchoice.text === choice && (
              <div
                key={index}
                className={`flex justify-between items-center ${'bg-[#F3F8F6]'} ${'w-52'} p-2 border border-slate-400 rounded-md cursor-pointer`}
              >
                <div
                  className={`bg-[#F3F8F6] cursor-pointer text-center px-1 rounded-md ${'w-48'} h-40 `}
                >
                  <img
                    className="max-w-[100%] max-h-[100%] inline text-center"
                    src={
                      qchoice?.image?.includes(
                        'https://images.unsplash.com/'
                      ) || qchoice?.image?.includes('.giphy.com/')
                        ? qchoice?.image
                        : process.env.NEXT_PUBLIC_S3_LINK + qchoice?.image
                    }
                  />
                  <span className="capitalize">{choice}</span>
                </div>
              </div>
            )
        )
      )}
    </div>
  );
};
const Upload = ({ answer, question }) => {
  if (!answer) {
    return '';
  }
  const form = JSON.parse(answer);
  // console.log(form, 'form');
  return (
    <div className="mt-8 flex gap-5">
      {form?.map((file, index) => (
        <a
          key={index}
          target="_blank"
          // role="button"
          class="bg-[#1ab886] p-2 mx--zero w-100 me-3 mb-3 flex items-center space-x-2"
          href={process.env.NEXT_PUBLIC_S3_LINK + file.image}
          download={file.image}
          // onClick={(e) => {
          //   e.preventDefault();
          //   window.location = process.env.NEXT_PUBLIC_S3_LINK + file.image;
          //   download(process.env.NEXT_PUBLIC_S3_LINK + file.image, file.image);
          // }}
        >
          <span class="pr--xl">{file.image}</span>
          <svg
            width="16"
            height="16"
            class="ml--xl"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.33325 3.33333H2.66659C2.31296 3.33333 1.97382 3.4738 1.72378 3.72385C1.47373 3.9739 1.33325 4.31304 1.33325 4.66666V13.3333C1.33325 13.687 1.47373 14.0261 1.72378 14.2761C1.97382 14.5262 2.31296 14.6667 2.66659 14.6667H11.9999C12.3535 14.6667 12.6927 14.5262 12.9427 14.2761C13.1928 14.0261 13.3333 13.687 13.3333 13.3333V9.33333"
              stroke="#FFFFFF"
              strokeWidth="1.5"
              strokeLinecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              d="M13.3334 2.66667V5.66667M13.3334 2.66667L6.66675 9.33334M13.3334 2.66667H9.84135"
              stroke="#FFFFFF"
              strokeWidth="1.5"
              strokeLinecap="round"
              stroke-linejoin="round"
            ></path>
          </svg>
        </a>
      ))}
    </div>
  );
};
const Questions = ({ question, answer, otherAnswer }) => {
  if (!answer || answer == '--skipped--' || answer == '--not answered--') {
    return answer;
  }
  return (
    <>
      {question?.questionType == 'Scale' && (
        <div className="bg-[#EC6772] mt-1 w-10 h-10 text-white flex items-center justify-center font-bold rounded-[6px] text-lg">
          {answer}
        </div>
      )}
      {question?.questionType === 'multiple_choice' && (
        <MultipleChoice answer={answer} otherAnswer={otherAnswer} />
      )}
      {question?.questionType === 'open_list' && <OpenList answer={answer} />}
      {question?.questionType === 'Rating' && (
        <Rating answer={answer} question={question} />
      )}
      {question?.questionType === 'Text' && answer}
      {question?.questionType === 'Email' && answer}
      {question?.questionType === 'Phone_Number' && '+' + answer}
      {question?.questionType === 'Number' && answer}
      {question?.questionType === 'Boolean' && answer}
      {question?.questionType === 'Slider' && answer}
      {question?.questionType === 'contact_form' && (
        <ContactForm answer={answer} question={question} />
      )}
      {question?.questionType === 'constant_sum' && (
        <ConstantSum answer={answer} question={question} />
      )}
      {question?.questionType === 'picture_choice' && (
        <PictureChoice answer={answer} question={question} />
      )}
      {question?.questionType === 'upload' && (
        <Upload answer={answer} question={question} />
      )}
    </>
  );
};
export default Questions;
