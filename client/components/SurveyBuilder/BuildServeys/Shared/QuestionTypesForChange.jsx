'use client';

// import UpgradePlanModal from '@/components/UpgradePlanModal/UpgradePlanModal';

import ProjectHooks from '@/features/projects/project.hooks';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { AiOutlineStar } from 'react-icons/ai';
import { BsInputCursorText, BsTelephone } from 'react-icons/bs';
import { CgMenuMotion } from 'react-icons/cg';
import { CiGrid32, CiSliderHorizontal } from 'react-icons/ci';
import { GrImage, GrMultiple } from 'react-icons/gr';
import { MdOutlineThumbsUpDown } from 'react-icons/md';
import { PiStairs } from 'react-icons/pi';
import { RiContactsBookLine } from 'react-icons/ri';
import { RxCross1 } from 'react-icons/rx';
import { TbChartGridDots, TbListNumbers, TbSum } from 'react-icons/tb';
import Icon from '../edit/IconComponent';
import QuestionExample from '../questionTypes/QuestionExample/QuestionExample';

const QuestionType = ({
  question,
  selectedQuestionType,
  setSelectedQuestionType,
}) => {
  const [isHovering, setIsHovering] = useState(false);

  return (
    <div
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <button
        onClick={() => {
          setSelectedQuestionType(question);
        }}
        className={`flex flex-col gap-1 justify-center items-center w-36 py-3 border text-primary rounded-lg duration-100 transition-all ${
          isHovering ||
          question?.questionType == selectedQuestionType?.questionType
            ? 'border-secondary bg-[#F5FFFC]'
            : 'border-slate-200 bg-white'
        }`}
      >
        <div
          className={`border rounded-full h-7 w-7 p-1 flex justify-center items-center transition-all duration-200 ${
            isHovering ||
            question?.questionType == selectedQuestionType?.questionType
              ? 'border-secondary bg-secondary'
              : 'border-[#E1F2EC] bg-[#F1FFFA]'
          }`}
        >
          <Icon
            color={
              isHovering ||
              question?.questionType == selectedQuestionType?.questionType
                ? 'white'
                : '#0E4449'
            }
            selectedQuestion={question}
          />
        </div>
        <span className="text-[13px] font-semibold ">{question?.name}</span>
      </button>
    </div>
  );
};

const QuestionTypesForChange = ({
  question,
  selectedQuestionData,
  setIsTypeChangeModalOpen,
  addQuestion,
  surveyQuestions,
  deleteAnswerData,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  // const { query } = router;
  const params = useParams();
  const projectId = params?.projectId;
  // const { projectId } = query;
  const [selectedQuestionType, setSelectedQuestionType] = useState({
    onClick: () => {
      selectedQuestionData({
        ...question,
        questionType: 'multiple_choice',
        questionTypeData: {
          default_answer: false,
          randomize: false,
          stacked: false,
          choices: [],
          multiple_answer: false,
          allow_other_option: false,
          none_of_above: false,
          all_of_above: false,
          selection_limit_type: 'unlimited',
          required: question?.questionTypeData?.required,
        },
      });
    },
    name: 'Multiple Choice',
    questionType: 'multiple_choice',
    icon: <GrMultiple size="16px" />,
  });

  // Fetch project data
  const { data, isFetching, isError, error } = ProjectHooks.useProject({
    id: projectId,
  });

  // Determine if the survey is paid
  const isPro = data?.audience_type === 'buy';

  const closeModal = () => {
    setIsOpen(false);
  };
  const [search, setSearch] = useState('');
  const essentialQuestions = [
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'multiple_choice',
          questionTypeData: {
            default_answer: false,
            randomize: false,
            stacked: false,
            choices: [''],
            multiple_answer: false,
            allow_other_option: false,
            none_of_above: false,
            all_of_above: false,
            selection_limit_type: 'unlimited',
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Multiple Choice',
      questionType: 'multiple_choice',
      icon: <GrMultiple size="16px" />,
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Text',
          questionTypeData: {
            default_answer: false,
            voice_transcription: false,
            type: 'single_line',
            Sentiment: false,
            translate_answers: false,
            default_answer_value: '',
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Open-ended Question',
      questionType: 'Text',
      icon: <BsInputCursorText size="16px" />,
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Scale',
          questionTypeData: {
            left_label: 'Least Likely',
            middle_label: 'Neutral',
            right_label: 'Most Likely',
            steps: '10',
            reverse_scale: false,
            required: question?.questionTypeData?.required,
            include_n_a: false,
            default_answer: false,
            default_answer_value: '7',
            tags: [],
          },
        });
      },
      questionType: 'Scale',
      name: 'Likert Scale',
      icon: (
        <svg
          className="inline"
          height="32"
          width="32"
          fill="none"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect height="32" width="32" fill="transparent" rx="3"></rect>
          <rect height="8" width="20" stroke="#63686F" x="6" y="10"></rect>
          <path
            d="M18.5 19.5L15 22.5355H22.0711L18.5 19.5Z"
            fill="#63686F"
          ></path>
        </svg>
      ),
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Rating',
          questionTypeData: {
            default_answer: false,
            default_answer_value: '5',
            required: question?.questionTypeData?.required,
            icon: 'AiOutlineStar',
            steps: '5',
          },
        });
      },
      name: 'Star Rating',
      questionType: 'Rating',
      icon: <AiOutlineStar className="inline" size="21px" />,
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'picture_choice',
          questionTypeData: {
            default_answer: false,
            choices: [{ image: '', text: '' }],
            skipLogicArray: [
              {
                displayLogicArray: [
                  {
                    logic_type: 'question',
                  },
                ],
              },
            ],
            displayLogicArray: [
              {
                logic_type: 'question',
              },
            ],
            required: question?.questionTypeData?.required,
            score: false,
            tags: [],
            fit_images: false,
            randomize: false,
            multiple_answer: false,
            allow_other_option: false,
            all_of_above: false,
            none_of_above: false,
            selection_limits: 'unlimited',
          },
        });
      },
      name: 'Picture Choice',
      questionType: 'picture_choice',
      icon: <GrImage className="inline opacity-60" size="18px" />,
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Boolean',
          questionTypeData: {
            default_answer: false,
            yes_label: 'Yes',
            no_label: 'No',
            icon: 'thumb',
            tag: [],
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Yes or No',
      questionType: 'Boolean',
      icon: <MdOutlineThumbsUpDown className="inline" size="18px" />,
    },
  ];
  const advancedQuestions = [
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Slider',
          questionTypeData: {
            slider_type: 'line',
            range_min: '1',
            range_max: '100',
            show_value: false,
            segments: 0,
            decimals: 0,
            start_pos: false,
            default_value: 1,
            total_to: '',
            steps: '5',
            tags: [],
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Slider',
      icon: <CiSliderHorizontal className="inline" size="18px" />,
      questionType: 'Slider',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Number',
          questionTypeData: {
            default_answer: false,
            min_value: '',
            max_value: '',
            default_answer_value: '',
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Number',
      icon: <TbListNumbers className="inline" size="18px" />,
      questionType: 'Number',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'constant_sum',
          questionTypeData: {
            start_pos: false,
            type: 'text',
            range_min: '0',
            range_max: '100',
            total_to: '100',
            show_total: true,
            required: question?.questionTypeData?.required,
            options: ['', ''],
          },
        });
      },
      name: 'Constant Sum',
      icon: <TbSum className="inline opacity-60" size="18px" />,
      questionType: 'constant_sum',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'matrix',
          questionTypeData: {
            randomize: false,
            type: 'radio',
            required: question?.questionTypeData?.required,
            matrix: [
              ['', '', ''],
              ['', '', ''],
              ['', '', ''],
            ],
          },
        });
      },
      name: 'Matrix',
      icon: <TbChartGridDots className="inline opacity-60" size="18px" />,
      questionType: 'matrix',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'open_list',
          questionTypeData: {
            default_answer: false,
            randomize: false,
            stacked: false,
            choices: [],
            multiple_answer: false,
            allow_other_option: false,
            none_of_above: false,
            all_of_above: false,
            selection_limits: 'unlimited',
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Open List',
      questionType: 'open_list',
      icon: <TbChartGridDots size="16px" />,
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'MatrixSlider',
          questionTypeData: {
            required: question?.questionTypeData?.required,
            range_max: '20',
            steps: '5',
            start_at_1: false,
            matrix: [
              ['', '1', '2', '3', '4', '5'],
              ['', '', '', '', '', ''],
              ['', '', '', '', '', ''],
              ['', '', '', '', '', ''],
            ],
          },
        });
      },
      name: 'Matrix Slider',
      icon: <CiGrid32 className="inline opacity-60" size="18px" />,
      questionType: 'MatrixSlider',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Ranking',
          questionTypeData: {
            required: question?.questionTypeData?.required,
            total_factors: 3,
            range: 3,
            factors: [{ factor: '' }, { factor: '' }, { factor: '' }],
          },
        });
      },
      name: 'Ranking',
      questionType: 'Ranking',
      icon: <PiStairs size="16px" />,
    },
  ];
  const contactQuestions = [
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Email',
          questionTypeData: {
            default_answer: false,
            default_answer_value: '',
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Email',
      icon: (
        <svg
          className="inline"
          height="32"
          width="32"
          fill="none"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect height="32" width="32" fill="transparent" rx="3"></rect>
          <path
            d="M24.0001 8L13.2827 18.7174"
            stroke="#63686F"
            strokeLinecap="round"
          ></path>
          <path
            d="M16.6087 25L13.2826 18.7174L7 15.3913L24 8L16.6087 25Z"
            stroke="#63686F"
            strokeLinecap="round"
          ></path>
        </svg>
      ),
      questionType: 'Email',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Phone_Number',
          questionTypeData: {
            default_answer: false,
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Phone',
      icon: <BsTelephone className="inline" size="18px" />,
      questionType: 'Phone_Number',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'contact_form',
          questionTypeData: {
            fields: [
              {
                type: 'text',
                label: 'name',
                required: 'true',
              },
              {
                type: 'email',
                label: 'email',
                required: 'true',
              },
              {
                type: 'text',
                label: 'address',
                required: 'true',
              },
              {
                type: 'text',
                label: 'address2',
                required: 'true',
              },
              {
                type: 'text',
                label: 'city',
                required: 'true',
              },
              {
                type: 'text',
                label: 'state',
                required: 'true',
              },
              {
                type: 'text',
                label: 'country',
                required: 'true',
              },
              {
                type: 'number',
                label: 'zip',
                required: 'true',
              },
              {
                type: 'text',
                label: 'company',
                required: 'true',
              },
            ],
            stack_field: 'horizontally',
            default_answer: false,
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Contact Form',
      questionType: 'contact_form',
      icon: (
        <RiContactsBookLine
          className="inline fill-[opacity(0.5)]"
          size="18px"
        />
      ),
    },
  ];
  const rawExperienceQuestions = [
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'Section',
          questionTypeData: {
            default_answer: false,
            button_label: 'Continue',
          },
        });
      },
      name: 'Section',
      icon: <CgMenuMotion className="inline" size="18px" />,
      questionType: 'Section',
    },
    {
      onClick: () => {
        selectedQuestionData({
          ...question,
          questionType: 'upload',
          questionTypeData: {
            default_answer: false,
            no_of_files: 1,
            filetype: ['image'],
            required: question?.questionTypeData?.required,
          },
        });
      },
      name: 'Upload',
      icon: <GrImage className="inline opacity-60" size="18px" />,
      questionType: 'upload',
    },
  ];
  const experienceQuestions = isPro
    ? rawExperienceQuestions.slice(0, 1)
    : rawExperienceQuestions;
  return (
    <div
      id="question-type-selection"
      className="h-[90vh] overflow-auto rounded-2xl"
    >
      <div className="bg-white rounded-2xl">
        <div className="py-3 rounded-t-2xl border-b shadow-md">
          <div className="flex justify-between items-center px-4">
            <h3 className="text-base font-medium text-center"> </h3>
            <h3 className="text-base font-medium text-center">
              Change Question Typeq
            </h3>
            <h3 className="text-base font-medium text-center">
              <RxCross1
                onClick={() => setIsTypeChangeModalOpen(false)}
                size={20}
                className="text-secondary"
              />
            </h3>
          </div>
          {/* <SearchBar setSearch={setSearch} search={search} /> */}
        </div>
        <div className="flex-1 bg-slate-200 flex max-w-6xl w-[978px]">
          <div className="p-4 bg-slate-200 w-1/2">
            <MapQuestionTypes
              search={search}
              essentialQuestions={essentialQuestions}
              advancedQuestions={advancedQuestions}
              contactQuestions={contactQuestions}
              experienceQuestions={experienceQuestions}
              selectedQuestionType={selectedQuestionType}
              setSelectedQuestionType={setSelectedQuestionType}
              isPro={isPro}
            />
          </div>
          <div className="bg-white w-1/2">
            <QuestionExample
              selectedQuestionType={selectedQuestionType}
              setIsModalOpen={setIsTypeChangeModalOpen}
              fromWhere="questionTypeChange"
              question={question}
              deleteAnswerData={deleteAnswerData}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const MapQuestionTypes = ({
  search,
  essentialQuestions,
  advancedQuestions,
  contactQuestions,
  experienceQuestions,
  selectedQuestionType,
  setSelectedQuestionType,
  isPro,
}) => {
  const hasEssentialQuestions = essentialQuestions.filter((f) =>
    f.name?.toLowerCase().includes(search?.toLowerCase())
  );
  const hasAdvancedQuestions = advancedQuestions.filter((f) =>
    f.name?.toLowerCase().includes(search?.toLowerCase())
  );
  const hasContactQuestions = contactQuestions.filter((f) =>
    f.name?.toLowerCase().includes(search?.toLowerCase())
  );
  const hasExpQuestions = experienceQuestions.filter((f) =>
    f.name?.toLowerCase().includes(search?.toLowerCase())
  );

  return (
    <>
      {(hasEssentialQuestions.length > 0 ||
        hasAdvancedQuestions.length > 0 ||
        hasContactQuestions.length > 0 ||
        hasExpQuestions.length > 0) && (
        <>
          {hasEssentialQuestions.length > 0 && (
            <div className="py-3">
              <p className="text-[13px] mb-2 font-normal text-primary">
                Essentials
              </p>
              <div className="flex flex-wrap items-center gap-3">
                {hasEssentialQuestions.map((question) => {
                  return (
                    <QuestionType
                      key={question.name}
                      question={question}
                      selectedQuestionType={selectedQuestionType}
                      setSelectedQuestionType={setSelectedQuestionType}
                    />
                  );
                })}
              </div>
            </div>
          )}
          {hasAdvancedQuestions.length > 0 && (
            <div className="py-3">
              <p className="text-[13px] mb-2 font-normal text-primary">
                Advanced
              </p>
              <div className="flex flex-wrap items-center gap-3">
                {hasAdvancedQuestions.map((question) => {
                  return (
                    <QuestionType
                      key={question.name}
                      question={question}
                      selectedQuestionType={selectedQuestionType}
                      setSelectedQuestionType={setSelectedQuestionType}
                    />
                  );
                })}
              </div>
            </div>
          )}
          {!isPro && hasContactQuestions.length > 0 && (
            <div className="py-3">
              <p className="text-[13px] mb-2 font-normal text-primary">
                Contact
              </p>
              <div className="flex flex-wrap items-center gap-3">
                {hasContactQuestions.map((question) => (
                  <QuestionType
                    key={question.name}
                    question={question}
                    selectedQuestionType={selectedQuestionType}
                    setSelectedQuestionType={setSelectedQuestionType}
                  />
                ))}
              </div>
            </div>
          )}

          {hasExpQuestions.length > 0 && (
            <div className="py-3">
              <p className="text-[13px] mb-2 font-normal text-primary">
                Others
              </p>
              <div className="flex flex-wrap items-center gap-3">
                {hasExpQuestions.map((question) => {
                  return (
                    <QuestionType
                      key={question.name}
                      question={question}
                      selectedQuestionType={selectedQuestionType}
                      setSelectedQuestionType={setSelectedQuestionType}
                    />
                  );
                })}
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default QuestionTypesForChange;
