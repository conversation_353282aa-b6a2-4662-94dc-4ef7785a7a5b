'use client';
import { errorToast } from '@/utils/toast';
import { useEffect, useState } from 'react';
import PreviewBottomBar from '../../edit/Reusables/PreviewBottomBar';

const MultipleChoiceView = ({ formData, deviceType }) => {
  const [choices, setChoices] = useState([]);
  const [selectedChoices, setSelectedChoices] = useState([]);
  const [isMultipleAnswer, setIsMultipleAnswer] = useState(false);
  const [selectionType, setSelectionType] = useState('unlimited');
  const [exactChoices, setExactChoices] = useState(0);
  const [choiceRange, setChoiceRange] = useState({ max: 1, min: 0 });

  useEffect(() => {
    const data = formData?.questionTypeData?.choices || [];

    if (formData?.questionTypeData?.randomize) {
      // Separate regular and special choices
      const specialChoices = [];
      const regularChoices = [];

      data.forEach((choice, index) => {
        const text = choice.text?.toLowerCase();
        if (
          choice.isOther ||
          text === 'all of the above' ||
          text === 'none of the above'
        ) {
          specialChoices.push({ choice, index }); // Save original index
        } else {
          regularChoices.push(choice);
        }
      });

      // Shuffle regular choices
      for (let i = regularChoices.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [regularChoices[i], regularChoices[j]] = [
          regularChoices[j],
          regularChoices[i],
        ];
      }

      // Rebuild the choices array with shuffled regular choices and original-position special choices
      const shuffledChoices = [];
      let regularIndex = 0;
      for (let i = 0; i < data.length; i++) {
        const special = specialChoices.find((s) => s.index === i);
        if (special) {
          shuffledChoices.push(special.choice);
        } else {
          shuffledChoices.push(regularChoices[regularIndex]);
          regularIndex++;
        }
      }

      setChoices(shuffledChoices);
    } else {
      setChoices(data);
    }

    if (formData?.questionTypeData?.default_answer) {
      if (formData?.questionTypeData?.multiple_answer) {
        setSelectedChoices([formData?.questionTypeData?.default_answer_value]);
      } else {
        setSelectedChoices(
          formData?.questionTypeData?.default_answer_value || []
        );
      }
    }

    setIsMultipleAnswer(formData?.questionTypeData?.multiple_answer);
    setSelectionType(formData?.questionTypeData?.selection_limit_type);
    setExactChoices(
      Number(formData?.questionTypeData?.selection_limit_exact_number)
    );
    setChoiceRange({
      max: Number(formData?.questionTypeData?.selection_limit_range_max),
      min: Number(formData?.questionTypeData?.selection_limit_range_min),
    });
  }, [formData?.questionTypeData]);

  const saveChoice = (choice) => {
    let choices = [...selectedChoices];
    if (choices.includes(choice)) {
      const index = choices.findIndex((item) => item === choice);
      if (index !== -1) {
        choices.splice(index, 1);
      }
    } else if (
      choices.some((choice) =>
        ['None of the above', 'All of the above'].includes(choice)
      )
    ) {
      choices = [choice];
    } else if (['None of the above', 'All of the above'].includes(choice)) {
      choices = [choice];
    } else {
      if (formData?.questionTypeData?.multiple_answer) {
        const type = formData?.questionTypeData?.selection_limit_type;
        if (type == 'unlimited') {
          choices.push(choice);
        } else if (type == 'exact_number') {
          const limit = Number(
            formData?.questionTypeData?.selection_limit_exact_number
          );
          if (limit > choices?.length) {
            choices.push(choice);
          } else {
            errorToast('Cannot select more than ' + limit + ' values');
          }
        } else if (type == 'range') {
          const max = Number(
            formData?.questionTypeData?.selection_limit_range_max
          );
          const min = Number(
            formData?.questionTypeData?.selection_limit_range_min
          );
          if (max > choices.length) {
            choices.push(choice);
          } else {
            errorToast(
              'Cannot select more than ' +
                max +
                ' or ' +
                'less than ' +
                min +
                ' values'
            );
          }
        } else {
          choices.push(choice);
        }
      } else choices = [choice];
    }
    setSelectedChoices([...choices]);
  };

  // Get responsive classes based on deviceType
  const getChoiceGridClasses = () => {
    const isStacked = formData?.questionTypeData?.stacked;

    if (isStacked) {
      return 'grid grid-cols-1 gap-4';
    }

    switch (deviceType) {
      case 'mobile':
        return 'grid grid-cols-1 gap-4';
      case 'tablet':
        return 'grid grid-cols-2 gap-4';
      default: // desktop
        return 'grid grid-cols-2 lg:grid-cols-3 gap-4';
    }
  };

  const getImageHeight = () => {
    switch (deviceType) {
      case 'mobile':
        return 'h-40';
      case 'tablet':
        return 'h-44';
      default: // desktop
        return 'h-48';
    }
  };

  console.log({ formData, choices });

  return (
    <div className="min-h-screen bg-slate-900 p-4">
      <div className="flex flex-col h-full max-w-6xl mx-auto">
        {/* Question Image */}
        {formData?.questionTypeData?.questionImage && (
          <div className="mb-6">
            <img
              // src={
              //   formData.questionTypeData.questionImage || '/placeholder.svg'
              // }
              src={
                formData?.questionTypeData?.questionImage
                  ? formData.questionTypeData.questionImage.startsWith(
                      'http://'
                    ) ||
                    formData.questionTypeData.questionImage.startsWith(
                      'https://'
                    )
                    ? formData.questionTypeData.questionImage
                    : process.env.NEXT_PUBLIC_S3_LINK +
                      formData.questionTypeData.questionImage
                  : '/placeholder-image.png'
              }
              alt="Question illustration"
              className={`w-full max-w-md mx-auto rounded-lg shadow-sm object-cover ${
                deviceType === 'mobile' ? 'max-h-48' : 'max-h-64'
              }`}
            />
          </div>
        )}
        {/* Question Section */}
        <div className="mb-6">
          <div className="text-white">
            <div className="flex items-start tracking-wider">
              <p className="lg:text-base ">{formData?.index + 1}. </p>
              <p
                className="lg:text-base"
                dangerouslySetInnerHTML={{ __html: formData?.question || '' }}
              />
            </div>
            {formData?.description && (
              <p
                className={`text-slate-300 mb-4 ${
                  deviceType === 'mobile' ? 'text-sm' : 'text-base'
                }`}
              >
                {formData?.description}
              </p>
            )}
          </div>
        </div>

        {/* Selection Instructions */}
        <div
          className={`mb-4 text-slate-300 ${
            deviceType === 'mobile' ? 'text-sm' : 'text-base'
          }`}
        >
          {isMultipleAnswer && (
            <>
              {selectionType === 'unlimited' &&
                'You can choose more than one answers'}
              {selectionType === 'exact_number' && (
                <>
                  {selectedChoices?.length != exactChoices && (
                    <>
                      {selectedChoices?.length == 0
                        ? `Choose any ${exactChoices}`
                        : `Choose ${
                            exactChoices - selectedChoices?.length
                          } more`}
                    </>
                  )}
                </>
              )}
              {selectionType === 'range' && (
                <>
                  {selectedChoices?.length != choiceRange.max && (
                    <>
                      {selectedChoices?.length == 0
                        ? `Make between ${choiceRange.min} and ${choiceRange.max} choices`
                        : `You can choose ${
                            choiceRange.max - selectedChoices?.length
                          } more`}
                    </>
                  )}
                </>
              )}
            </>
          )}
        </div>

        {/* Choices Grid */}
        <div className={getChoiceGridClasses()}>
          {choices?.map((choice, index) => (
            <div
              key={index}
              onClick={() => saveChoice(choice.text)}
              className={` rounded-lg overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] p-2  ${
                selectedChoices?.includes(choice.text)
                  ? 'bg-white text-gray-700'
                  : 'bg-[#FFFFFF1A] text-white'
              }`}
            >
              {choice?.image ? (
                <div className="relative">
                  <img
                    src={
                      choice?.image
                        ? choice.image.startsWith('http://') ||
                          choice.image.startsWith('https://')
                          ? choice.image
                          : process.env.NEXT_PUBLIC_S3_LINK + choice.image
                        : '/placeholder-image.png'
                    }
                    alt={`Choice ${choice.text}`}
                    className={`w-full object-cover rounded-lg ${getImageHeight()}`}
                  />
                </div>
              ) : null}

              {/* Choice Selection Area */}
              <div className="pt-2">
                <div className="flex items-center gap-3">
                  <>
                    {/* Checkbox or Radio Button */}
                    {isMultipleAnswer ? (
                      <div className="relative pt-1">
                        <input
                          type="checkbox"
                          checked={selectedChoices?.includes(choice.text)}
                          onChange={() => saveChoice(choice.text)}
                          className="w-5 h-5  border-2 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                        />
                      </div>
                    ) : (
                      <div className="relative pt-1">
                        <input
                          type="radio"
                          name={`question-${formData?.index}`}
                          checked={selectedChoices?.includes(choice.text)}
                          onChange={() => saveChoice(choice.text)}
                          className="w-5 h-5  border-2 border-gray-300 focus:ring-green-500 focus:ring-2"
                        />
                      </div>
                    )}
                  </>

                  {/* Choice Text */}
                  <label
                    className={` font-medium cursor-pointer ${
                      deviceType === 'mobile' ? 'text-sm' : 'text-base'
                    }`}
                  >
                    {choice.text}
                  </label>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Other Option Input */}
        {selectedChoices.includes('Other') &&
          formData?.questionTypeData?.allow_other_option && (
            <div className="mt-6">
              <input
                type="text"
                placeholder="Please specify..."
                className={`w-full border border-slate-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-slate-800 text-white placeholder-slate-400 ${
                  deviceType === 'mobile' ? 'p-3 text-sm' : 'p-4 text-base'
                }`}
              />
            </div>
          )}

        {/* Bottom Bar */}
        <div className="mt-8">
          <PreviewBottomBar
            isLastQuestion={formData?.isLastQuestion}
            required={formData?.questionTypeData?.required}
          />
        </div>
      </div>
    </div>
  );
};

export default MultipleChoiceView;
