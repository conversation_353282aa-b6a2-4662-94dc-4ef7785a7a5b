const mongoose = require('mongoose');
const appServer = require('./app');
const configs = require('./src/app/configs/configs');
const { logger } = require('./utils/logger');
const localRedis = require('./src/app/utils/localRedis');
mongoose.set('strictQuery', true);

let server;

const myServer = (name) => {
  return appServer().listen(configs.port, () => {
    logger.info(`Environment: ${configs.node_env}`);
    logger.info(
      `${
        configs.node_env === 'production'
          ? `Production Database ${name}`
          : `Development/Staging Database ${name}`
      } & Server listening on port ${configs.port}`
    );
  });
};

// Connect to MongoDB
Promise.all([
  mongoose.connect(configs.mongodb_uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  }),
  localRedis,
])
  .then(
    ([
      {
        connection: { name },
      },
    ]) => {
      logger.info('MongoDB connected');
      if (configs.node_env === 'development') {
        myServer(name);
      } else {
        server = myServer(name);
      }
    }
  )
  .catch((err) => {
    logger.error(err);
    shutdown('SIGINT');
    process.exit(1);
  });

// Graceful shutdown handler
const shutdown = (signal) => async () => {
  try {
    logger.warn(`${signal} received: closing server...`);

    if (server?.close) {
      server.close(() => {
        logger.info('HTTP server closed.');
      });
    }

    await mongoose.connection.close();
    logger.info('MongoDB connection closed.');

    process.exit(0);
  } catch (err) {
    logger.error('Error during shutdown', err);
    process.exit(1);
  }
};

// Process signals
process.on('SIGINT', () => shutdown('SIGINT'));
process.on('SIGTERM', () => shutdown('SIGTERM'));

// Handle uncaught errors
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  shutdown('UnhandledRejection')();
});

process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  shutdown('UncaughtException')();
});
