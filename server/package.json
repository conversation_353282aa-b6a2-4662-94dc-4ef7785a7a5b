{"name": "standard-insights-server", "version": "1.0.10", "description": "", "main": "server.js", "author": {"name": "Standard Insights", "email": "<EMAIL>", "url": "https://standard-insights.com"}, "engines": {"node": ">=14.0.0", "npm": ">=10.0.0", "yarn": "please-use-pnpm", "pnpm": ">=8.0.0"}, "scripts": {"start": "node server.js ", "dev": "nodemon server.js", "staging": "node server.js", "prettier": "prettier --write .", "lint": "eslint \"./**/*.js\" --quiet", "lintAll": "eslint .", "lintFull": "eslint \"./**/*.js\"", "lintFix": "eslint --fix .", "deploy": "serverless deploy", "prepare": "test -d .git && husky install || echo 'skip husky'", "test": "jest"}, "lint-staged": {"**/*.js": ["eslint --ext .js --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "nodemonConfig": {"ignore": ["uploads/*", "temp/*", "txts/*", "files/*"]}, "keywords": [], "license": "ISC", "dependencies": {"@akash3080/chartjs-node": "^1.0.5", "@akash3080/html-to-pdf-node": "^1.0.3", "@anthropic-ai/sdk": "^0.32.1", "@aws-lambda-powertools/logger": "^2.19.1", "@aws-sdk/client-s3": "^3.800.0", "@aws-sdk/client-sqs": "^3.810.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@bull-board/api": "^6.9.6", "@bull-board/express": "^6.9.2", "@getbrevo/brevo": "^2.2.0", "@kurkle/color": "^0.3.2", "@mistralai/mistralai": "^1.7.2", "@sendgrid/mail": "^8.1.4", "@sentry/node": "^9.20.0", "@sentry/profiling-node": "^9.20.0", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "bluebird": "^3.7.2", "bullmq": "^5.53.0", "chartjs-plugin-datalabels": "^2.2.0", "compression": "^1.7.4", "connect-ensure-login": "^0.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.0.1", "ejs": "^3.1.8", "express": "^4.18.1", "express-fileupload": "^1.4.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.5.1", "express-session": "^1.18.1", "firebase-admin": "^11.0.1", "geoip-lite": "^1.4.10", "google-spreadsheet": "^4.1.4", "googleapis": "^118.0.0", "helmet": "^5.1.1", "html-to-text": "^9.0.5", "html2pptxgenjs": "^0.0.3", "htmlparser2": "^9.1.0", "http-status": "^1.7.3", "infogram": "^0.3.1", "ioredis": "^5.4.1", "joi": "^17.6.0", "jsonwebtoken": "^9.0.2", "lancaster-stemmer": "^2.1.0", "lodash": "^4.17.21", "mammoth": "^1.8.0", "moment": "^2.30.1", "mongoose": "^6.13.8", "mongoose-aggregate-paginate-v2": "^1.1.4", "mongoose-encryption": "^2.1.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-html-parser": "^6.1.13", "nodemailer": "^6.9.13", "openai": "^4.10.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "pptxgenjs": "^3.12.0", "prom-client": "^15.1.3", "rate-limit-redis": "^4.2.0", "selenium-webdriver": "^4.12.0", "serverless-http": "^3.2.0", "showdown": "^2.1.0", "srt-parser-2": "^1.2.3", "stemmer": "^2.0.1", "stripe": "^13.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "translatte": "^3.0.1", "ua-parser-js": "^2.0.3", "uuid": "^9.0.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/multer": "^1.4.12", "commitlint": "^19.8.1", "eslint": "^8.56.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^8.0.3", "jest": "^29.3.1", "lint-staged": "^16.1.2", "nodemon": "^3.1.7", "prettier": "^3.5.3"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}