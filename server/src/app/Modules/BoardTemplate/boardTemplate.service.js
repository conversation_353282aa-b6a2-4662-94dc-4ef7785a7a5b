const BoardBlock = require('../BoardBlock/boardBlock.model');
const BoardTemplate = require('./boardTemplate.model');
const Board = require('../Board/board.model');
const Report = require('../Report/report.model');
const Team = require('../Team/team.model');
const ProjectMedia = require('../media/media.model');
const { default: mongoose } = require('mongoose');
const { uploadFile } = require('../../../../utils/s3');
const configs = require('../../configs/configs');

const createBoardTemplateIntoDB = async (
  reportId,
  tabId,
  userId,
  image,
  body
) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const board = await Board.findOne({ tabId }).select('_id').lean();

    if (!board) {
      throw new Error('No board found!');
    }

    const boardBlocks = await BoardBlock.find({
      reportId: reportId,
      boardId: board._id,
    }).lean();

    if (!boardBlocks || boardBlocks.length === 0) {
      throw new Error('The board is empty!');
    }

    const { team } = await Report.findById(reportId).select('team').lean();

    const templateId = new mongoose.Types.ObjectId();

    const blocksToInsert = boardBlocks.map(
      ({ _id, createdAt, updatedAt, ...rest }) => ({
        ...rest,
        isTemplate: true,
        templateId,
      })
    );

    // Insert board blocks as template
    await BoardBlock.insertMany(blocksToInsert, { session });

    // Upload file (this is outside DB, so cannot rollback)
    const upload = await uploadFile(image); // Consider how to handle failures here

    // Create media reference
    await ProjectMedia.create(
      [
        {
          teamId: team,
          mimeType: 'image/png',
          originalname: upload.Key,
          name: upload.Key,
          key: upload.Key,
          link: configs.s3_link + upload.Key,
          size: image.size,
          references: [
            {
              type: 'board_template_snap',
              id: templateId,
            },
          ],
        },
      ],
      { session }
    );

    // Update team storage
    await Team.findByIdAndUpdate(
      team,
      { $inc: { storageUsed: image.size } },
      { session }
    );

    // Create board template
    const payload = {
      ...body,
      _id: templateId,
      reportId,
      tabId,
      snapKey: upload.Key,
      boardId: board._id,
      teamId: team,
      createdBy: userId,
    };

    const boardTemplate = await BoardTemplate.create([payload], { session });

    if (!boardTemplate || boardTemplate.length === 0) {
      throw new Error('Failed to create Board Template!');
    }

    await session.commitTransaction();
    session.endSession();

    return boardTemplate[0]; // return the inserted document
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
};

const getAllBoardTemplateFromDB = async (reportId, userId, query) => {
  const { page = 1, limit = 10, search = '' } = query;
  // 1️⃣ fetch the report’s team
  const { team } = await Report.findById(reportId).select('team').lean();

  // 2️⃣ build your privacy & search filters
  const filters = [
    {
      $or: [
        { privacy: 'public' },
        { privacy: 'si_team' },
        { privacy: 'my_team', teamId: team },
        { privacy: 'only_me', createdBy: mongoose.Types.ObjectId(userId) },
      ],
    },
  ];
  if (search) {
    filters.push({
      $or: [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ],
    });
  }

  // 3️⃣ assemble aggregate with lookup for blocks
  const blockColl = BoardBlock.collection.name; // usually "boardblocks"
  // const aggregate = BoardTemplate.aggregate([
  //   { $match: { $and: filters } },
  //   {
  //     $lookup: {
  //       from: blockColl,
  //       let: { templateId: '$_id' },
  //       pipeline: [
  //         {
  //           $match: {
  //             $expr: {
  //               $expr: { $eq: ['$templateId', '$$templateId'] },
  //             },
  //           },
  //         },
  //         { $sort: { order: 1 } },
  //         { $limit: 6 },
  //       ],
  //       as: 'blocks',
  //     },
  //   },
  //   { $match: { blocks: { $ne: [] } } },
  //   { $sort: { createdAt: -1 } },
  // ]);
  const aggregate = BoardTemplate.aggregate([
    { $match: { $and: filters } },
    {
      $lookup: {
        from: blockColl,
        localField: '_id',
        foreignField: 'templateId',
        as: 'blocks',
      },
    },
    { $addFields: { blocks: { $slice: ['$blocks', 6] } } }, // limit to 6
    { $sort: { createdAt: -1 } },
  ]);

  // 4️⃣ paginate
  const options = { page, limit, lean: true };
  const templates = await BoardTemplate.aggregatePaginate(aggregate, options);
  return templates || [];
};

const useBoardTemplateIntoDB = async (reportId, tabId, templateId) => {
  const board = await Board.findOne({ tabId }).select('_id').lean();
  if (!board) {
    throw new Error('No board found!');
  }
  const template = await BoardTemplate.findById(templateId)
    .select('reportId boardId _id')
    .lean();
  if (!template) {
    throw new Error('No template found!');
  }
  const templateBlocks = await BoardBlock.find({
    reportId: template.reportId,
    boardId: template.boardId,
    isTemplate: true,
    templateId: template?._id,
  }).lean();
  if (!templateBlocks || templateBlocks?.length === 0) {
    throw new Error('No blocks found!');
  }
  // 4️⃣ Prepare new block docs targeting our new report/board
  const blocksToInsert = templateBlocks.map(
    ({ _id, createdAt, updatedAt, isTemplate, templateId, ...rest }) => ({
      ...rest,
      reportId,
      boardId: board._id,
    })
  );

  // 5️⃣ Bulk-insert the cloned blocks
  const insertedBlocks = await BoardBlock.insertMany(blocksToInsert);
  return insertedBlocks;
};

const updateBoardTemplateInDB = async (templateId, userId, body) => {
  const template = await BoardTemplate.findById(templateId)
    .select('_id createdBy')
    .lean();
  if (!template) {
    throw new Error('No template found!');
  }

  if (userId != template.createdBy) {
    throw new Error('You are not authorized to update this template!');
  }

  const updatedTemplate = await BoardTemplate.findByIdAndUpdate(
    templateId,
    { ...body },
    { new: true }
  );
  if (!updatedTemplate) throw new Error('Failed to update board template!');
  return updatedTemplate;
};

const deleteBoardTemplateFromDB = async (templateId, userId) => {
  const template = await BoardTemplate.findById(templateId)
    .select('_id createdBy')
    .lean();
  if (!template) {
    throw new Error('No template found!');
  }
  if (userId != template.createdBy) {
    throw new Error('You are not authorized to delete this template!');
  }

  await BoardTemplate.findByIdAndDelete(templateId, {
    new: true,
  });
  return {
    _id: templateId,
  };
};

const BoardTemplateServices = {
  createBoardTemplateIntoDB,
  getAllBoardTemplateFromDB,
  useBoardTemplateIntoDB,
  updateBoardTemplateInDB,
  deleteBoardTemplateFromDB,
};

module.exports = { BoardTemplateServices };
