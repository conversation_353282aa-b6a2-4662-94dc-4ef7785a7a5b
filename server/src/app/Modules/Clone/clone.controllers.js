const catchAsync = require('../../utils/catchAsyncError');
const {
  cloneIntoDB: cloneProjectIntoDB,
  getCloningStatusFromDB,
  getAllClonesOfAInsightFromDB,
  cloneInsightIntoProjectDB,
} = require('./clone.services');
require('./clone.queue');
const createClone = catchAsync(async (req, res) => {
  const clone = await cloneProjectIntoDB({
    projectId: req.params.id,
    teamId: req.params.team,
  });
  return res.status(200).json(clone);
});
const cloneInsightToProject = catchAsync(async (req, res) => {
  console.log({
    insightId: req.params.id,
    teamId: req.params.team,
    userId: req?.user?.user_id,
  });
  const clone = await cloneInsightIntoProjectDB({
    insightId: req.params.id,
    teamId: req.params.team,
    userId: req?.user?.user_id,
  });
  return res.status(200).json(clone);
});
const getCloningStatus = catchAsync(async (req, res) => {
  const clone = await getCloningStatusFromDB(req.params.id, req.query.type);
  return res.status(200).json(clone);
});
const getAllClonesOfAnInsight = catchAsync(async (req, res) => {
  const clones = await getAllClonesOfAInsightFromDB({
    teamId: req.params.teamId,
    insightId: req.params.insightId,
  });
  return res.status(200).json({ data: clones, message: 'Success' });
});

module.exports = {
  createClone,
  getCloningStatus,
  getAllClonesOfAnInsight,
  cloneInsightToProject,
};
