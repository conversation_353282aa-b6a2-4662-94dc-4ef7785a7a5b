const axios = require('axios');
const fsPromise = require('fs/promises');
const fs = require('fs');
const _ = require('lodash');

const { toObjectId } = require('../../../../utils/toObjectId');
const AiQuantitative = require('./quantitative.model');
const AiQualitative = require('./qualitative.model');
const AiSavedViewFilter = require('./ai_filters.model');
const AiSavedViewComparison = require('./ai_comparison.model');
const AiSavedViewSplit = require('./ai_splits.model');
const { ai_questionTemplate, ai_sectionTemplate } = require('./ai.templates');
// const { RecursiveCharacterTextSplitter } = require('@langchain/textsplitters');
const Survey = require('../../../../models/Survey');
const configs = require('../../configs/configs');
const {
  checkRankingQuestion,
  checkSingleQuestion,
  checkOpenQuestion,
  checkMatrixQuestion,
} = require('../../../../utils/typeCheck');
const mistralClient = require('../../llmClient/mistralClient');

const find_similar_documents = async (embedding, surveyId, type, viewId) => {
  return new Promise((resolve, reject) => {
    switch (type) {
      case 'quantitative':
        AiQuantitative.aggregate([
          {
            $vectorSearch: {
              index: 'advance',
              path: 'embedding',
              queryVector: embedding,
              numCandidates: 20,
              limit: 10,
              filter: { $and: [{ project_id: toObjectId(surveyId) }] },
            },
          },
          // { $match: { project_id: toObjectId(surveyId) } },
          { $project: { text_chunks: 1, project_id: 1 } },
        ])
          .then((data) => {
            resolve(data);
          })
          .catch((err) => reject(err));
        break;
      case 'qualitative':
        AiQualitative.aggregate([
          {
            $vectorSearch: {
              index: 'advance',
              path: 'embedding',
              queryVector: embedding,
              numCandidates: 20,
              limit: 10,
              // filter: { $and: [{ project_id: toObjectId(surveyId) }] },
            },
          },
          { $match: { project_id: toObjectId(surveyId) } },
          { $project: { text_chunks: 1, project_id: 1 } },
        ])
          .then((data) => {
            resolve(data);
          })
          .catch((err) => reject(err));
        break;
      case 'filter':
        AiSavedViewFilter.aggregate([
          {
            $vectorSearch: {
              index: 'advance',
              path: 'embedding',
              queryVector: embedding,
              numCandidates: 20,
              limit: 10,
              filter: {
                $and: [
                  {
                    project_id: toObjectId(surveyId),
                    viewId: toObjectId(viewId),
                  },
                ],
              },
            },
          },
          // {
          //   $match: {
          //     project_id: toObjectId(surveyId),
          //     viewId: toObjectId(viewId),
          //   },
          // },
          { $project: { text_chunks: 1, project_id: 1, viewId: 1 } },
        ])
          .then((data) => {
            resolve(data);
          })
          .catch((err) => reject(err));
        break;
      case 'comparison':
        AiSavedViewComparison.aggregate([
          {
            $vectorSearch: {
              index: 'advance',
              path: 'embedding',
              queryVector: embedding,
              numCandidates: 20,
              limit: 10,
              filter: {
                $and: [
                  {
                    project_id: toObjectId(surveyId),
                    viewId: toObjectId(viewId),
                  },
                ],
              },
            },
          },
          // {
          //   $match: {
          //     project_id: toObjectId(surveyId),
          //     viewId: toObjectId(viewId),
          //   },
          // },
          { $project: { text_chunks: 1, project_id: 1, viewId: 1 } },
        ])
          .then((data) => {
            resolve(data);
          })
          .catch((err) => reject(err));
        break;
      case 'split':
        AiSavedViewSplit.aggregate([
          {
            $vectorSearch: {
              index: 'advance',
              path: 'embedding',
              queryVector: embedding,
              numCandidates: 20,
              limit: 10,
              filter: {
                $and: [
                  {
                    project_id: toObjectId(surveyId),
                    viewId: toObjectId(viewId),
                  },
                ],
              },
            },
          },
          // {
          //   $match: {
          //     project_id: toObjectId(surveyId),
          //     viewId: toObjectId(viewId),
          //   },
          // },
          { $project: { text_chunks: 1, project_id: 1, viewId: 1 } },
        ])
          .then((data) => {
            resolve(data);
          })
          .catch((err) => reject(err));
        break;
      case 'dataWaveCompared':
        AiSavedViewComparison.aggregate([
          {
            $vectorSearch: {
              index: 'advance',
              path: 'embedding',
              queryVector: embedding,
              numCandidates: 20,
              limit: 10,
              filter: {
                $and: [
                  {
                    project_id: toObjectId(surveyId),
                    viewId: toObjectId(viewId),
                  },
                ],
              },
            },
          },
          { $project: { text_chunks: 1, project_id: 1, viewId: 1 } },
        ])
          .then((data) => {
            resolve(data);
          })
          .catch((err) => reject(err));
        break;
      default:
        reject('No type provided!');
    }
  });
};

const find_similar_vectors = async (embedding, surveyId, type, viewId) => {
  switch (type) {
    case 'quantitative':
      // eslint-disable-next-line no-case-declarations
      const documents1 = await AiQuantitative.aggregate([
        {
          $match: {
            $and: [
              {
                project_id: toObjectId(surveyId),
              },
            ],
          },
        },
      ]);
      // eslint-disable-next-line no-case-declarations
      let context1 = '';

      for (const doc of documents1) {
        context1 += doc['text_chunks'].replace('\n', ' ');
      }

      return context1;
    case 'qualitative':
      // eslint-disable-next-line no-case-declarations
      const documents2 = await AiQualitative.aggregate([
        {
          $match: {
            $and: [
              {
                project_id: toObjectId(surveyId),
                viewId: toObjectId(viewId),
              },
            ],
          },
        },
      ]);
      // eslint-disable-next-line no-case-declarations
      let context2 = '';

      for (const doc of documents2) {
        context2 += doc['text_chunks'].replace('\n', ' ');
      }

      return context2;
    case 'filter':
      // eslint-disable-next-line no-case-declarations
      const documents3 = await AiSavedViewFilter.aggregate([
        {
          $match: {
            $and: [
              {
                project_id: toObjectId(surveyId),
                viewId: toObjectId(viewId),
              },
            ],
          },
        },
      ]);

      // eslint-disable-next-line no-case-declarations
      let context3 = '';

      for (const doc of documents3) {
        context3 += doc['text_chunks'].replace('\n', ' ');
      }

      return context3;
    case 'comparison':
      // eslint-disable-next-line no-case-declarations
      const documents4 = await AiSavedViewComparison.aggregate([
        {
          $match: {
            $and: [
              {
                project_id: toObjectId(surveyId),
                viewId: toObjectId(viewId),
              },
            ],
          },
        },
      ]);

      // eslint-disable-next-line no-case-declarations
      let context4 = '';

      for (const doc of documents4) {
        context4 += doc['text_chunks'].replace('\n', ' ');
      }

      return context4;
    case 'split':
      // eslint-disable-next-line no-case-declarations
      const documents5 = await AiSavedViewSplit.aggregate([
        {
          $match: {
            $and: [
              {
                project_id: toObjectId(surveyId),
                viewId: toObjectId(viewId),
              },
            ],
          },
        },
      ]);
      // eslint-disable-next-line no-case-declarations
      let context5 = '';

      for (const doc of documents5) {
        context5 += doc['text_chunks'].replace('\n', ' ');
      }

      return context5;
  }
};
const get_embedding = async (text) => {
  text = text.replace('\n', ' '); // Replace newline characters
  try {
    const res = await mistralClient.createEmbeddings({ message: text });
    return res.data[0].embedding;
  } catch (err) {
    console.error('Error fetching embedding:', err);
    throw err;
  }
};

const getQNA = async (question, surveyId, type, viewId = '') => {
  const question_embedding = await get_embedding(question);

  // const documents = await find_similar_documents(
  //   question_embedding,
  //   surveyId,
  //   type,
  //   viewId
  // );

  const documents = await find_similar_vectors(
    question_embedding,
    surveyId,
    type,
    viewId
  );

  // const context = documents;

  // console.log(documents);

  // for (const doc of documents) {
  //   context += doc['text_chunks'].replace('\n', ' ');
  // }

  const template = `
    You are an expert who loves to help people! Given the following context sections, answer the
    question using only the given context or previous interactions. If you are unsure and the answer is not
    explicitly written in the documentation, say "Sorry, I don't know how to help with that. "

    Context sections: ${documents}

    Question:
    ${question}

     Notes:
    - Find question and answer from the context
    - Always try to answer from the context and Find the question from context, If any question start with what and how then Find answer of question from the context. Not about your self.
    - If any question is not related to the context, then say "Sorry, I don't know how to help with that."
    - If any question is related to the context, please answer it. otherwise response own message
    - Try to answer in details.
    - Do not give any answer about your self like (I'm sorry for any confusion, but as a model I don't have personal characteristics or a physical age.), Only provide the response following the context
    - The question is not about Your self, it is about the context. So not answer about your self. Find answer of question from the context.

    Answer:
    `;
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;
  // return new Promise((resolve, reject) => {
  //   axios
  //     .post('http://127.0.0.1:8000/python/ai/quantitative/chat', {
  //       database: 'ai_surveys',
  //       collection: collection,
  //       query: question,
  //       token: '6QrTzAzABIJSSz6602t339ElAN89z5AKVCb933Sj',
  //     })
  //     .then((data) => resolve(data))
  //     .catch((err) => reject(err));
  // });
};
const getQNAStream = async (question, surveyId, type) => {
  const question_embedding = await get_embedding(question);
  const documents = await find_similar_documents(
    question_embedding,
    surveyId,
    type
  );

  let context = '';

  for (const doc of documents) {
    context += doc['text_chunks'].replace('\n', ' ');
  }
  const template = `
    You are an expert who loves to help people! Given the following context sections, answer the
    question using only the given context or previous interactions. If you are unsure and the answer is not
    explicitly written in the documentation, say "Sorry, I don't know how to help with that."

    Context sections:
    ${context}

    Question:
    ${question}

    Answer:
    `;
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;

  // .then((response) => {
  //   let text = '';
  //   response.data.on('data', (chunk) => {
  //     // console.log('Received chunk:', chunk.toString());
  //     const isText = chunk.toString().split('data: ')[1];
  //     if (isText && isText != '[DONE]') {
  //       const json = JSON.parse(chunk.toString().split('data: ')[1]);
  //       text += json.choices[0].delta.content;
  //       console.clear();
  //       console.log(text);
  //     }
  //   });

  // response.data.on('end', () => {
  //   console.log('Stream ended');
  // });

  // response.data.on('error', (err) => {
  //   console.error('Stream error:', err);
  // });
  // stream.data.split('data: ').slice(1, stream.data.split('data: ').length);
  // console.log();
  // })
  // .catch((err) => {
  //   console.log(err);
  //   reject(err);
  // });
};

const getQnaPython = async (question, surveyId, type) => {
  return new Promise((resolve, reject) => {
    axios
      .post('http://127.0.0.1:8000/python/ai/quantitative/chat', {
        // database: 'ai_surveys',
        projectId: surveyId,
        token: '6QrTzAzABIJSSz6602t339ElAN89z5AKVCb933Sj',
        query: question,
        type,
      })
      .then((data) => {
        resolve(data.data.response[0].content);
      })
      .catch((err) => reject(err));
  });
};

const getQuestionAnalysis = async (
  question,
  surveyId,
  type,
  lang,
  viewId,
  detailLevel
) => {
  // console.log('353', viewId);
  const question_embedding = await get_embedding(question);
  const documents = await find_similar_documents(
    question_embedding,
    surveyId,
    type,
    viewId
  );

  let context = '';

  for (const doc of documents) {
    context += doc['text_chunks'].replace('\n', ' ');
  }

  const survey = await Survey.findById(surveyId);

  let promptInstructions = [];
  if (detailLevel === 'brief') {
    promptInstructions = [
      'Summarize the main idea in one or two sentences.',
      'Give a brief overview of the topic.',
    ];
  } else if (detailLevel === 'detailed') {
    promptInstructions = [
      'Provide a detailed analysis with various elements.',
      'Explain the topic extensively, covering multiple aspects.',
    ];
  }

  const promptInstruction = promptInstructions.join(' ');

  const template = ai_questionTemplate({
    context,
    question,
    type,
    lang,
    templateType: survey.templateType,
    detailLevel: promptInstruction,
  });
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;
};

const getSectionAnalysis = async (
  question,
  surveyId,
  type,
  lang,
  section,
  viewId,
  chartData = [],
  detailLevel
) => {
  const question_embedding = await get_embedding(question);
  const documents = await find_similar_documents(
    question_embedding,
    surveyId,
    type,
    viewId
  );

  let context = '';

  for (const doc of documents) {
    context += doc['text_chunks'].replace('\n', ' ');
  }

  const template = ai_sectionTemplate({
    context,
    question,
    section,
    lang,
    type,
    chartData,
    detailLevel,
  });
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;
};

const mistralQNA = async (question) => {
  return (await mistralClient.chatCompletion({ message: question })).choices[0]
    .message;
};

async function createTxt(text, filePath) {
  try {
    // Write the text to the file
    await fsPromise.writeFile(filePath, text, 'utf8');

    // Return the file path on successful write
    return filePath;
  } catch (err) {
    // If there's an error, throw it so it can be handled by the caller
    throw new Error(`Error creating TXT file: ${err.message}`);
  }
}

// Function to concatenate text from a DataFrame-like object
function concatenateText(df, title, additional) {
  let text = '';
  try {
    const columns = Object.keys(df[0]); // Assuming df is an array of objects
    df.forEach((row) => {
      text += `${row[columns[1]]}.\n ${row[columns[2]]}.\n ${
        row[columns[3]]
      }.\n ${row[columns[4]]}.\n`;
    });
    text = `Survey Title: ${title}\n\n\nRelevant information about survey:\n ${additional}\n${text}`;
  } catch (error) {
    text = `Survey Title: ${title}\n\n\nRelevant information about survey:\n ${additional}\n${JSON.stringify(
      df,
      null,
      2
    )}`;
  }
  return text;
}
function processInterview(body, text) {
  if (text == null) {
    text = '';
  }
  body.forEach((line) => {
    text += `${line} \n `;
  });
  return text;
}

function recursiveTextSplitter(text, chunkSize, chunkOverlap) {
  const chunks = []; // Initialize an empty array to store chunks
  let startIdx = 0; // Start index for chunking

  // While there is text left to chunk
  while (startIdx < text.length) {
    // End index of the current chunk
    const endIdx = Math.min(startIdx + chunkSize, text.length);

    // Extract the chunk from text
    const chunk = text.slice(startIdx, endIdx);

    // Add chunk to the chunks array
    chunks.push(chunk);

    // Calculate the new start index for the next chunk, including overlap
    startIdx += chunkSize - chunkOverlap;
  }

  return chunks; // Return all chunks
}

async function dataPrep(file, projectId, viewId = '') {
  // Split data
  // const textSplitter = recursiveCharacterTextSplitter({
  //   chunkSize: 1500,
  //   chunkOverlap: 20,
  //   separators: ['\n\n', '\n', '(?<=\\. )', ' ', ''],
  //   lengthFunction: (text) => text.length,
  // });

  const txt = fs.readFileSync(file, { encoding: 'utf8' });
  // const textChunks = await textSplitter.splitText(txt);
  const textChunks = await recursiveTextSplitter(txt, 1450, 20);

  const array = [];

  // Calculate embeddings and store into MongoDB
  for (let i = 0; i < textChunks.length; i++) {
    const text = textChunks[i];
    const embedding = await get_embedding(text);
    const data = { text_chunks: text, embedding };
    data.project_id = projectId;
    if (viewId) data.viewId = viewId;
    array.push(data);
  }
  return array;
}

const splitProcess = (raw, splitBy) => {
  let finalText = '';
  for (let j = 0; j < raw.length; j++) {
    if (typeof raw[j] === 'object') {
      const question = raw[j]['question'];
      let text = '';
      if (question === splitBy) continue;

      if (raw[j]['splitQuestionAnswers']) {
        for (const [key, value] of Object.entries(
          raw[j]['splitQuestionAnswers']
        )) {
          const total = Object.values(value).reduce(
            (acc, curr) => acc + (curr ?? 0),
            0
          );
          text += `. For split '${key}' the answers are the following (${total} total responses): `;
          for (const [key1, value1] of Object.entries(value)) {
            const percentage = (
              Math.round(((value1 ?? 0) / total) * 10000) / 100
            ).toFixed(2);
            text += `, ${key1} with ${value1} (${percentage}%) responses `;
          }
        }
      }
      finalText += `For the question '${question}' we have split the responses by the question ${splitBy} with these results: ${text}\n`;
    }
  }
  return finalText;
};

const comparisonProcess = (data) => {
  let finalText = '';
  for (let j = 0; j < data.length; j++) {
    if (typeof data[j] === 'object') {
      let text = '';
      if (['matrix'].includes(data[j]['questionType'])) {
        const answersOrder = data[j]['answerOrders'];
        const datasets = data[j]['chartData']['datasets'];
        const compareOptions = data[j]['comparisonMatrix'];
        const question = data[j]['question'];
        let initial = 1;

        text = `For person overall (Person #${initial}) the answers for question '${question}' rate in the following way:`;
        datasets.forEach((opt) => {
          const total = opt['data'].reduce((acc, curr) => acc + (curr ?? 0), 0);
          const label = opt['label'];
          opt['data'].forEach((value, x) => {
            const percentage = (
              Math.round(((value ?? 0) / total) * 10000) / 100
            ).toFixed(2);
            text += `, response ${answersOrder[x]} with label ${label} had ${value} responses of ${total} (${percentage}%)`;
          });
        });
        text += ';';

        for (const [person, dataPerson] of Object.entries(compareOptions)) {
          initial += 1;
          text += ` compared to person ${person} (Person #${initial}) the answers for question '${question}' rate in the following way: `;
          dataPerson.forEach((opt) => {
            const total = opt['data'].reduce(
              (acc, curr) => acc + (curr ?? 0),
              0
            );
            const label = opt['label'];
            opt['data'].forEach((value, x) => {
              const percentage = (
                Math.round(((value ?? 0) / total) * 10000) / 100
              ).toFixed(2);
              text += `, response '${answersOrder[x]}' with label '${label}' had ${value} responses of ${total} (${percentage}%)`;
            });
          });
        }
      } else if (['rank', 'Ranking'].includes(data[j]['questionType'])) {
        const answersOrder = data[j]['chartData']['labels'];
        const datasets = data[j]['chartData']['datasets'];
        const question = data[j]['question'];
        let laps = 0;

        datasets.forEach((opt) => {
          if (laps === 0) {
            text += `For the question '${question}' answered by ${opt['label']} respondents, rank the options in the following way:`;
          } else {
            text += `compared to the same question ('${question}') answered by ${opt['label']} respondents, rank the options in the following way:`;
          }

          let total = 0;

          opt['rankArray']?.forEach((rank) => {
            const values = Object.values(rank);
            total += values.reduce((acc, curr) => acc + curr, 0);
          });

          // const total = Object.values(opt['rankArray'][0]).reduce(
          //   (acc, curr) => acc + (curr ?? 0),
          //   0
          // );
          opt['rankArray']?.forEach((resp, x) => {
            text += ` option ${answersOrder[x]}`;
            for (const [lab, respondents] of Object.entries(resp)) {
              const percentage = (
                Math.round(((respondents ?? 0) / total) * 10000) / 100
              ).toFixed(2);
              text += ` ${respondents} of ${total} (${percentage}%) answered ${lab},`;
            }
          });
          text += '  .';
          laps += 1;
        });
      } else if (
        ['excel', 'single', 'multiple_choice'].includes(data[j]['questionType'])
      ) {
        const datasets = data[j]['chartData']['datasets'];
        const question = data[j]['question'];
        let answersOrder = [];

        try {
          answersOrder = data[j]['chartData']['labels'];
        } catch {
          answersOrder = data[j]['answerOrders'];
        }

        let laps = 0;
        datasets.forEach((opt) => {
          if (laps === 0) {
            text += `For the question '${question}' answered by ${opt['label']} respondents, the results were the following: `;
          } else {
            text += ` compared to the same question ('${question}') answered by ${opt['label']} respondents, the results were the following: `;
          }
          const total = opt['data'].reduce((acc, curr) => acc + (curr ?? 0), 0);
          opt['data'].forEach((value, x) => {
            const percentage = (
              Math.round(((value ?? 0) / total) * 10000) / 100
            ).toFixed(2);
            text += `, response ${answersOrder[x]} had ${value} responses of ${total} (${percentage}%)`;
          });
          laps += 1;
        });
      } else {
        const datasets = data[j]['chartData']['datasets'];
        const question = data[j]['question'];
        let answersOrder = [];

        try {
          answersOrder = data[j]['chartData']['labels'];
        } catch {
          answersOrder = data[j]['answerOrders'];
        }

        let laps = 0;
        datasets.forEach((opt) => {
          if (laps === 0) {
            text += `For the question '${question}' answered by ${opt['label']} respondents, the results were the following: `;
          } else {
            text += ` compared to the same question ('${question}') answered by ${opt['label']} respondents, the results were the following: `;
          }
          const total = opt['data'].reduce((acc, curr) => acc + (curr ?? 0), 0);
          opt['data'].forEach((value, x) => {
            const percentage = (
              Math.round(((value ?? 0) / total) * 10000) / 100
            ).toFixed(2);
            text += `, response ${answersOrder[x]} had ${value} responses of ${total} (${percentage}%)`;
          });
          laps += 1;
        });
      }
      text += '\n';
      finalText += text;
    }
  }

  return finalText;
};

const filteredProcess = async (data) => {
  let finalText = '';
  for (let j = 0; j < data.length; j++) {
    if (typeof data[j] === 'object') {
      let text = '';
      if (['matrix'].includes(data[j]['questionType'])) {
        const answersOrder = data[j]['answerOrders'];
        const datasets = data[j]['chartData']['datasets'];
        const question = data[j]['question'];
        text = `For the question '${question}' we received the following responses: `;
        datasets?.forEach((opt) => {
          const total = opt['data'].reduce((acc, curr) => acc + (curr ?? 0), 0);
          const label = opt['label'];
          opt['data']?.forEach((value, x) => {
            const percentage = (
              Math.round(((value ?? 0) / total) * 10000) / 100
            ).toFixed(2);
            text += `, the response ${answersOrder?.[x]} with label ${label} had ${value} responses of ${total} (${percentage}%)`;
          });
        });
        text += ';';
      } else if (['rank'].includes(data[j]['questionType'])) {
        const answersOrder = data[j]['chartData']['labels'];
        const datasets = data[j]['chartData']['datasets'];
        const question = data[j]['question'];
        let laps = 0;

        datasets.forEach((opt) => {
          if (laps === 0) {
            text += `For the question '${question}' answered by ${opt['label']} respondents, rank the options in the following way:`;
          } else {
            text += `compared to the same question ('${question}') answered by ${opt['label']} respondents, rank the options in the following way:`;
          }
          const total = Object.values(opt['rankArray'][0]).reduce(
            (acc, curr) => acc + (curr ?? 0),
            0
          );
          opt['rankArray'].forEach((resp, x) => {
            text += ` option ${answersOrder[x]}`;
            for (const [lab, respondents] of Object.entries(resp)) {
              const percentage = (
                Math.round(((respondents ?? 0) / total) * 10000) / 100
              ).toFixed(2);
              text += ` ${respondents} of ${total} (${percentage}%) answered ${lab},`;
            }
          });
          text += '  .';
          laps += 1;
        });
      } else if (['Ranking'].includes(data[j]['questionType'])) {
        const d = {};
        data?.[j]?.['chartData']?.['labels']?.forEach((item, i) => {
          d[item] = data?.[j]?.['chartData']?.['datasets']?.[0]?.['data']?.[i];
        });
        const message = `Based on the labels and the chartData make me an analysis the data is related to a ranking quesiton of a survey, \n

                  ${JSON.stringify(d)}
                  `;
        const res = await mistralClient.chatCompletion({ message });

        if (res.choices[0]?.message?.content) {
          text +=
            `For the question ${data?.[j]?.['question']}` +
            res.choices[0].message.content;
        }
      } else if (
        ['excel', 'single', 'multiple_choice'].includes(data[j]['questionType'])
      ) {
        const datasets = data[j]['chartData']['datasets'];
        const question = data[j]['question'];
        let answersOrder = [];

        try {
          answersOrder = data[j]['chartData']['labels'];
        } catch {
          answersOrder = data[j]['answerOrders'];
        }

        let laps = 0;
        datasets.forEach((opt) => {
          if (laps === 0) {
            text += `For the question '${question}' answered by ${opt['label']} respondents, the results were the following: `;
          } else {
            text += ` compared to the same question ('${question}') answered by ${opt['label']} respondents, the results were the following: `;
          }
          const total = opt['data'].reduce((acc, curr) => acc + (curr ?? 0), 0);
          opt['data'].forEach((value, x) => {
            const percentage = (
              Math.round(((value ?? 0) / total) * 10000) / 100
            ).toFixed(2);
            text += `, response ${answersOrder[x]} had ${value} responses of ${total} (${percentage}%)`;
          });
          laps += 1;
        });
      } else {
        // Other question types can be handled here
      }
      text += '\n';
      finalText += text;
    }
  }
  return finalText;
};

const preprocessView = async (raw, typeView, splitBy) => {
  if (typeView === 'split') {
    return splitProcess(raw, splitBy);
  } else if (typeView === 'comparison') {
    return comparisonProcess(raw);
  } else if (typeView === 'filter') {
    return await filteredProcess(raw);
  } else if (typeView === 'dataWaveCompared') {
    return 'asdfasdfasd';
  } else {
    return false;
  }
};

const personaDetails = async (context) => {
  const template = `You are assisting us in creating a detailed persona that will help our team better understand and engage with a key target segment. This persona will guide strategies for marketing, product development, customer service, and business decisions.

To develop this persona, we’ll provide custom fields containing data about demographics, behaviors, motivations, challenges, and goals. Using this information, please generate the following output:

1. **Name**:
    - Based on the context data provided below, suggest a persona name that reflects the core traits and behaviors of the target group.
    - The name should be **localized to the market**. Ensure the name is culturally relevant and easily relatable within the market.
2. **Description**:

    Provide a concise description of the persona based on the data from the custom fields.

    - **Demographics**: Include information such as age, gender, occupation, location, income level, and other key demographic details.
    - **Behaviors and Habits**: Summarize the persona’s engagement with brands, products, or services, focusing on how they interact with the market (e.g., early adopter, price-sensitive buyer, brand loyalist, etc.).
    - **Motivations and Values**: Detail the key motivations that drive their decision-making, such as cost-consciousness, innovation, sustainability, or convenience.
    - **Challenges and Pain Points**: Identify the primary challenges or pain points the persona faces, which may impact their buying behavior or engagement.
    - **Goals and Needs**: Outline the persona’s key goals, whether practical or emotional, and what they hope to achieve by engaging with the product or service.
response will be only in json only. i only need 1 persona json only dont send multiple persona json.
${context}.
the response will be like {name: "your suggested name", desc: "your desc"}`;
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;
};
const personaAnalysis = async (
  questions,
  section,
  persona,
  lang,
  surveyId,
  viewId
) => {
  // console.log('353', viewId);
  const question_embedding = await get_embedding(questions);
  const documents = await find_similar_documents(
    question_embedding,
    surveyId,
    'filter',
    viewId
  );

  let context = '';

  for (const doc of documents) {
    context += doc['text_chunks'].replace('\n', ' ');
  }

  const template = `Based on the following persona profile, generate a realistic, professional response of this individual that visually represents their characteristics. The persona's response should reflect their demographic, location, and core traits in a natural, situational background, aligning with their described lifestyle. Avoid text or branding on the response. use the context section to generate the points mentiond in the summery based on the questions section.

### Persona Details:

1. **Profession**: ${persona.profession}

      Section Name:
${section}

Context sections:
${context}

Questions:
${questions}

Please help summarize the analysis and structure your summary as in the example below:
The section "Demographics and Vehicle Preferences" provides a detailed analysis of respondents' backgrounds and vehicle-related choices:

**Vehicle Ownership:** Gas motorbikes are the most owned vehicles, followed by gas cars. Electric motorbike ownership is lower but rising among younger age groups and urban dwellers. Ownership patterns vary by age, gender, and location, with gas motorbikes being the most popular overall.

**Car Type Ownership:** SUVs are the most popular car type among all demographics and locations, with Jakarta having the highest percentage of SUV owners. Sedans are less popular among the 18-24 age group, while MPVs are more popular among males and the 54-65 age group.

**Intention to Purchase a Car:** The majority across all demographics and locations plan to purchase a car in the next 12 months, indicating a strong market for car sales. Intention to purchase is highest among the 25-34 and 18-24 age groups living in Surabaya.

**Preferred Car Body Style:** Sedans and SUVs are the preferred body styles across all demographics and locations, with slight variations in preferences by age and location.

**Preferred Fuel Type:** Gasoline is the most popular fuel type, but there is notable interest in electric vehicles, especially among females and the 25-34 age group. Gasoline was also the preferred choice across locations, with Surabaya showing a balanced interest in all three fuel types.

**Attitude Towards Electric Vehicles (EVs):** Participants generally have a positive attitude towards electric vehicles, with the majority scoring 8-10. Positive attitudes are highest in Medan and Surabaya, while rural areas and Bandung showed a more varied response.
          `;
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;
};
const personaScore = async (context, title) => {
  console.log(context);
  const template = `**Instructions:**

You are evaluating a persona on a specific criterion. Use the context provided to generate a score out of 10. Provide a concise, assertive explanation for the score that highlights the key insights from the context without unnecessary detail. The explanation should be **clear, to the point**, and focused on the **most critical aspects** of the persona’s performance on the criterion. maintain the Strict policy.

Contexts:
Title: ${title}
Context for Scoring: ${context},\n

  response be like: {"title": the title i gave you,
  "score": the score you give by analysing,
  "desc": the desc you generated fromt he context}

  response will be only in json only. i only need 1 persona json only dont send multiple persona json. and the response will be only the json nothing else. only the the 1st json you generate as response always.

  dont send any response like below which has multiple json(Strict):
  {
"title": "asdfdd3",
"score": 9,
"desc": "Ai's passion for technology, dedication to their project, and focus on the most significant demographic group (18-24 age group) contribute to a high score. Their comfortable and focused workspace further enhances their effectiveness."
}

For John Doe's persona, I would score him a 10, highlighting his meticulous approach, detail-oriented nature, and his commitment to maintaining a work-life balance.

{
"title": "asdfdd3",
"score": 10,
"desc": "John's methodical approach to data analysis and commitment to maintaining a work-life balance result in a perfect score. His meticulous attention to detail ensures accurate insights, while his breaks and engagement with colleagues prevent burnout."
}

always send only 1 and dont add any text of yours


### Guidance for the **Desc**:

- Focus on **key insights**: What are the critical factors that influenced the score?
- Be **concise**: Use no more than 1-2 sentences.
- Be **assertive**: Use confident language that delivers valuable information directly, avoiding vague or overly descriptive phrasing.`;
  // console.log(template);
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;
};

const personaOverviewAnalysis = async (context, name) => {
  const template = `**Instructions:**

You are analyzing the performance of multiple personas on a specific criterion. Your task is to synthesize the scores across all personas and provide a comparative analysis. Additionally, calculate and include the **average score**, identify the **persona with the lowest score**, and the **persona with the highest score**. Keep the analysis **concise, comparative,** and **focused on the critical aspects** that distinguish each persona's performance.

Context:
Criterion Name: ${name},
${context}.\n
now give me a summery based on the context.
`;
  console.log(template);
  return (await mistralClient.chatCompletion({ message: template })).choices[0]
    .message;
};

const quantiDataProcessForAi = async (data) => {
  data.map((question) => {
    const answers = [];
    const chartData = new Map();
    const otherOptions = [];
    const singleOptions = new Map();

    question.answers.forEach((answer, answerIndex) => {
      if (answer.answer == '') {
        return;
      }
      if (answer.other) {
        otherOptions.push(answer.answer);
        if (!chartData.has('Others' || 'others' || 'other' || 'Other')) {
          chartData.set('Other', 1);
          return;
        }
        chartData.set('Other', chartData.get('Other') + 1);
        return;
      }
      if (answer.otherField !== '') {
        if (checkRankingQuestion(question?.questionType)) {
          if (!chartData.has(answer.otherField)) {
            chartData.set(answer.otherField, { [answer.answer]: 1 });
            return;
          }
          const chartDataObj = chartData.get(answer.otherField);
          chartData.set(
            answer.otherField,
            // eslint-disable-next-line no-prototype-builtins
            chartDataObj.hasOwnProperty(answer.answer)
              ? {
                  ...chartData.get(answer.otherField),
                  [answer.answer]:
                    chartData.get(answer.otherField)[answer.answer] + 1,
                }
              : {
                  ...chartData.get(answer.otherField),
                  [answer.answer]: 1,
                }
          );
          if (answerIndex + 1 === question.answers.length) {
            const values = Array.from(chartData.values());

            const labels = Array.from(chartData.keys());
            const sumArray = values.map((obj) => {
              const sum = Object.entries(obj).reduce(
                (acc, [key, value]) => acc + value * key * 1,
                0
              );
              return sum;
            });

            const dataV = sumArray?.map((item) => {
              return (
                labels.length -
                item /
                  (question.answers.length /
                    Array.from(chartData.keys()).length)
              ).toFixed(2);
            });
            labels?.map((label, index) => {
              chartData.set(label, {
                average: dataV[index],
                ranks: values[index],
              });
            });
          }
          return;
        } else if (checkSingleQuestion(question?.questionType)) {
          singleOptions.has(answer.otherField)
            ? singleOptions.set(answer.otherField, [
                ...singleOptions.get(answer.otherField),
                answer.answer,
              ])
            : singleOptions.set(answer.otherField, [answer.answer]);
          if (!chartData.has(answer.otherField)) {
            chartData.set(answer.otherField, 1);
            return;
          }
          return;
        } else {
          if (!chartData.has(answer.otherField)) {
            chartData.set(answer.otherField, [
              { label: answer.answer, data: 1 },
            ]);
            return;
          }
          const otherFieldData = chartData.get(answer.otherField);
          const filteredIndex = otherFieldData?.findIndex(
            (item) => item.label === answer.answer
          );
          if (filteredIndex > -1) {
            otherFieldData[filteredIndex].data += 1;
          } else {
            otherFieldData.push({ label: answer.answer, data: 1 });
          }
          chartData.set(answer.otherField, otherFieldData);
          if (answerIndex + 1 === question.answers.length) {
            const values = Array.from(chartData.values());
            const labels = Array.from(chartData.keys());
          }
          return;
        }
      }

      let answerArray = answer.answer.split('\n');
      if (answer.answer.includes(',') && question?.questionIndex != 2)
        answerArray = answer.answer.split(',');
      answerArray.forEach((item) => {
        if (!chartData.has(item)) {
          chartData.set(item, 1);
          return;
        }
        chartData.set(item, chartData.get(item) + 1);
      });
    });

    if (checkRankingQuestion(question?.questionType)) {
      question.sort = false;
    }
    question.otherOptions = otherOptions;
    question.singleOptions = Object.fromEntries(singleOptions);
    if (checkMatrixQuestion(question?.questionType)) {
      console.log(chartData, 'chartData for matrix');
      const values = Array.from(chartData.values());
      console.log(values, 'values');
      const dataV = [];
      values?.map((item) => {
        //pro 4 , 4
        _.isArray(item) &&
          item?.map((value) => {
            const valueIndex = dataV.findIndex(
              (item) => item.label === value.label
            );
            if (valueIndex > -1) {
              dataV[valueIndex].data.push(value.data);
            } else {
              dataV.push({
                label: value.label,
                data: [value.data],
              });
            }
          });
      });

      const data = dataV
        ?.sort((a, b) => (a.label > b.label ? 1 : b.label > a.label ? -1 : 0))
        ?.map((item, index) => {
          const dataObj = {
            data: item.data,
            label: item.label,

            stack: 'Stack 0',
          };
          return dataObj;
        });
      question.chartData = {
        labels: Array.from(chartData.keys()),
        datasets: data,
      };
    } else if (checkRankingQuestion(question?.questionType)) {
      const obj = Object.fromEntries(chartData);
      const newChartData = Object.keys(obj)
        .sort()
        .reduce((r, k) => ((r[k] = obj[k]), r), {});
      const values = Object.values(newChartData);
      const keys = Object.keys(newChartData);
      const avgValues = values.map((obj) => obj.average);
      const ranks = values.map((obj) => obj.ranks);

      const sortedRanks = values
        .sort((a, b) => parseFloat(b.average) - parseFloat(a.average))
        .map((f) => f.ranks);
      question.chartData = {
        labels: keys,
        datasets: [
          {
            data: avgValues,
            rankArray: ranks,
            label: 'Rank',
          },
        ],
      };
      // Create an array of objects with brand and price
      const products = avgValues.map((value, index) => ({
        key: keys[index],
        value,
      }));

      console.log(question.chartData, 'for ranking ');

      // Sort the products array based on price in ascending order
      products.sort((a, b) => parseFloat(b.value) - parseFloat(a.value));
      // Update the brands and prices arrays based on the sorted products
      const data = [];
      const labels = [];
      products
        .map((obj) =>
          Object.keys(obj)
            .sort()
            .reduce((r, k) => ((r[k] = obj[k]), r), {})
        )
        .forEach((product, index) => {
          labels[index] = product.key;
          data[index] = product.value;
        });

      question.chartDataSorted = {
        labels: labels,
        datasets: [
          {
            data: data,
            label: 'Rank',

            rankArray: sortedRanks,
          },
        ],
      };
    } else if (checkOpenQuestion(question?.questionType)) {
      const unFilteredKey = Array.from(chartData.keys());
      const allAnswersIndex = unFilteredKey.findIndex(
        (f) => f.toLowerCase() == 'all answers'
      );
      const keys = unFilteredKey.filter(
        (f) => f.toLowerCase() != 'all answers'
      );
      const newChartData = Array.from(chartData.values()).filter(
        (f, i) => i != allAnswersIndex
      );
      question.chartData = {
        labels: keys,
        datasets: [
          {
            data: newChartData,
            label: 'label',
          },
        ],
      };
      question.allAnswers = Array.from(chartData.values())[allAnswersIndex];
    } else if (checkSingleQuestion(question?.questionType)) {
      const obj = Object.fromEntries(chartData);
      const newChartData = Object.keys(obj)
        .sort()
        .reduce((r, k) => ((r[k] = obj[k]), r), {});
      const values = Object.values(newChartData);
      const keys = Object.keys(newChartData);
      question.chartData = {
        labels: keys,
        datasets: [
          {
            data: values,
            label: 'label',
          },
        ],
      };
    } else {
      const obj = Object.fromEntries(chartData);
      const newChartData = Object.keys(obj)
        .sort()
        .reduce((r, k) => ((r[k] = obj[k]), r), {});

      question.chartData = {
        labels: Object.keys(newChartData),
        datasets: [
          {
            data: Object.values(newChartData),
            label: checkRankingQuestion(question?.questionType)
              ? 'Rank'
              : question.question,
          },
        ],
      };
    }
    return question;
  });

  return data;
};

module.exports = {
  getQNA,
  getQNAStream,
  getQnaPython,
  mistralQNA,
  getQuestionAnalysis,
  getSectionAnalysis,
  concatenateText,
  createTxt,
  processInterview,
  dataPrep,
  preprocessView,
  splitProcess,
  comparisonProcess,
  filteredProcess,
  personaDetails,
  personaAnalysis,
  personaScore,
  personaOverviewAnalysis,
  quantiDataProcessForAi,
  find_similar_vectors,
};
