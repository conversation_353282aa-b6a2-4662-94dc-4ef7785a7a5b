const { default: mongoose } = require('mongoose');
const path = require('path');
const fs = require('fs');
const httpStatus = require('http-status');

const { uploadFile, deleteFile } = require('../../../../utils/s3');
const SavedView = require('./savedView.model');
const { logger } = require('../../../../utils/logger');
const AiSavedViewFilter = require('../AiAnalyzer/ai_filters.model');
const AiSavedViewSplit = require('../AiAnalyzer/ai_splits.model');
const AiSavedViewComparison = require('../AiAnalyzer/ai_comparison.model');
const {
  preprocessView,
  createTxt,
  concatenateText,
} = require('../AiAnalyzer/ai_analyzer.util');
const { processSavedView } = require('./savedView.queue');
const AppError = require('../../error/AppError');
const Persona = require('../Persona/persona.model');
const savedViewModel = require('./savedView.model');
const configs = require('../../configs/configs');

const createSavedView = async (payload, survey, metadata = '', isPersona) => {
  const session = await mongoose.connection.startSession();
  logger.info('Create saved view: Session started');

  let savedView;
  try {
    await session.startTransaction();
    logger.info('Create saved view: Transection started');
    savedView = new SavedView({
      ...payload.payload,
      ai_analyzer_status: 'processing',
      ai_analyzer_msg: '',
    });

    //process the splitBy, comparison, filter data
    const processedData = await preprocessView(
      survey,
      savedView.viewType,
      payload?.splitBy
    );

    //default filepath to save the txt file of processed data
    const txtFilePath = path.join(configs.tempDir, 'tmp');
    //check if directory exits or not, if not create
    if (!fs.existsSync(txtFilePath)) {
      fs.mkdirSync(txtFilePath, { recursive: true });
    }
    //concate the text
    const text = concatenateText(processedData, savedView.name, metadata);

    //craete a txt file from text and save in the local storage
    createTxt(
      text,
      path.join(configs.tempDir, 'tmp', savedView._id + '.txt')
    ).then(async (data) => {
      await processSavedView({ viewId: savedView._id });
    });

    let persona;
    if (isPersona) {
      persona = new Persona({
        name: 'No name',
        profession: savedView.name,
        survey: savedView.survey,
        savedView: savedView._id,
      });
      const allPersonas = await Persona.find({
        survey: savedView.survey,
      })
        .select('persona_scores')
        .limit(1);
      if (allPersonas.length > 0) {
        persona.persona_scores = allPersonas[0].persona_scores?.map((c) => {
          return { title: c.title, desc: '', score: 0 };
        });
      }
      await persona.save({ session });
      savedView.persona = persona._id;
    }
    await savedView.save({ session });

    await session.commitTransaction();
    logger.info('Create saved View: Transection committed');

    // savedView.ai_analyzer_txt_path =
    return savedView;
  } catch (err) {
    console.log(err);
    const txtFilePath = path.join(
      configs.tempDir,
      'tmp',
      savedView?._id + '.txt'
    );
    if (fs.existsSync(txtFilePath)) {
      fs.unlinkSync(txtFilePath);
    }
    await session.abortTransaction();
    logger.info('Create Saved view: Transection aborted');
    throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, err?.message);
  } finally {
    await session.endSession();
    logger.info('Create saved View: Transection ended');
  }
};
const updateSavedView = async (
  payload,
  survey,
  metadata = '',
  isPersona,
  viewId
) => {
  console.log({ payload, survey, viewId, isPersona });
  const session = await mongoose.connection.startSession();
  logger.info('update saved view: Session started');
  let savedView;
  try {
    await session.startTransaction();
    logger.info('update saved view: Transection started');
    // Find and update the document
    const savedView = await savedViewModel.findByIdAndUpdate(
      viewId,
      {
        $set: {
          ...payload.payload,
          ai_analyzer_status: 'processing',
          ai_analyzer_msg: '',
        },
      },
      { new: true }
    );
    // savedView = new SavedView({
    //   ...payload.payload,
    //   ai_analyzer_status: 'processing',
    //   ai_analyzer_msg: '',
    // });

    //process the splitBy, comparison, filter data
    const processedData = await preprocessView(
      survey,
      savedView.viewType,
      payload?.splitBy
    );
    //default filepath to save the txt file of processed data
    const txtFilePath = path.join(configs.tempDir, 'tmp');
    //check if directory exits or not, if not create
    if (!fs.existsSync(txtFilePath)) {
      fs.mkdirSync(txtFilePath, { recursive: true });
    }
    //concate the text
    const text = concatenateText(processedData, savedView.name, metadata);

    //craete a txt file from text and save in the local storage
    createTxt(
      text,
      path.join(configs.tempDir, 'tmp', savedView._id + '.txt')
    ).then(async (data) => {
      await processSavedView({ viewId: savedView._id });
    });

    let persona;
    if (isPersona) {
      persona = new Persona({
        name: 'No name',
        profession: savedView.name,
        survey: savedView.survey,
        savedView: savedView._id,
      });
      const allPersonas = await Persona.find({
        survey: savedView.survey,
      })
        .select('persona_scores')
        .limit(1);
      if (allPersonas.length > 0) {
        persona.persona_scores = allPersonas[0].persona_scores?.map((c) => {
          return { title: c.title, desc: '', score: 0 };
        });
      }
      await persona.save({ session });
      savedView.persona = persona._id;
    }
    await savedView.save({ session });

    await session.commitTransaction();
    logger.info('Create saved View: Transection commited');

    // savedView.ai_analyzer_txt_path =
    return savedView;
  } catch (err) {
    console.log(err);
    const txtFilePath = path.join(
      configs.tempDir,
      'tmp',
      savedView?._id + '.txt'
    );
    if (fs.existsSync(txtFilePath)) {
      fs.unlinkSync(txtFilePath);
    }
    await session.abortTransaction();
    logger.info('Create Saved view: Transection aborted');
    throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, err?.message);
  } finally {
    await session.endSession();
    logger.info('Create saved View: Transection ended');
  }
};

const retrySaveViewAiAnalyzer = async (viewId) => {
  const txtFilePath = path.join(configs.tempDir, 'tmp', viewId + '.txt');
  if (fs.existsSync(txtFilePath)) {
    await processSavedView({ viewId });
    return true;
  } else {
    throw new AppError('Retry failed! please delete this view and try again!');
  }
};
const getAllSavedViewOfASurvey = async (surveyId) => {
  const savedViews = await SavedView.find({ survey: surveyId });
  return savedViews;
};
const addNewSection = async (viewId, payload, files) => {
  const body = { ...payload };
  let upload;

  if (files?.file) {
    upload = await uploadFile(files.file);
    body.img = upload.Key;
  }

  const update = await SavedView.findByIdAndUpdate(
    viewId,
    { $push: { sections: body } },
    { new: true }
  ).select('sections');

  return update;
};

const addNewSubSection = async (viewId, sectionId, payload) => {
  const update = await SavedView.findOneAndUpdate(
    { _id: viewId, 'sections._id': sectionId },
    {
      $push: { 'sections.$.subSection': payload },
    },
    { new: true }
  ).select('sections');

  return update;
};

const deleteSubSection = async (viewId, sectionId, subSectionId) => {
  const update = await SavedView.findOneAndUpdate(
    { _id: viewId, 'sections._id': sectionId },
    {
      $pull: { 'sections.$.subSection': { _id: subSectionId } },
    },
    { new: true }
  ).select('sections');

  return update;
};

const updateASection = async (viewId, sectionId, payload, files) => {
  const survey = await SavedView.findById(viewId).select('sections');
  const sections = [...survey.sections];
  const section = sections.find((f) => f?._id?.toString() == sectionId);

  const body = { ...payload };

  let upload;
  if (files?.file && section?.img) {
    await deleteFile(section?.img);
  }

  if (files?.file) {
    upload = await uploadFile(files.file);
    body.img = upload.Key;
  }

  const update = await SavedView.findByIdAndUpdate(
    viewId,
    {
      sections: sections.map((f) => {
        if (f?._id?.toString() == sectionId) {
          return { ...body, _id: sectionId };
        }
        return f;
      }),
    },
    { new: true }
  ).select('sections');

  return update;
};

const deleteASection = async (viewId, sectionId) => {
  const survey = await SavedView.findById(viewId).select('sections');
  const section = survey.sections.find((f) => f._id?.toString() == sectionId);

  if (section && section?.img) {
    await deleteFile(section?.img);
  }

  const update = await SavedView.findByIdAndUpdate(
    { _id: viewId },
    { $pull: { sections: { _id: sectionId } } },
    { new: true }
  ).select('sections');

  return update;
};

const updateASavedView = async (viewId, payload) => {
  const view = await SavedView.findByIdAndUpdate(viewId, payload, {
    new: true,
  }).select(Object.keys(payload));
  return view;
};

const updateQuestionVisibility = async (viewId, payload) => {
  const { questionId, questionIndex, isHighLighted, isHidden } = payload;

  if (!questionId) {
    throw new Error('Question ID is required.');
  }

  const view = await SavedView.findById(viewId);
  if (!view) {
    throw new Error('View not found.');
  }

  const question = view.questions.find((q) => q.questionId === questionId);

  if (question) {
    if (isHighLighted !== undefined) question.isHighLighted = isHighLighted;
    if (isHidden !== undefined) question.isHidden = isHidden;
  } else {
    view.questions.push({
      questionId,
      questionIndex,
      isHighLighted: isHighLighted ?? false,
      isHidden: isHidden ?? false,
    });
  }

  view.questions.sort((a, b) => a.questionIndex - b.questionIndex);

  const updatedView = await view.save();

  return updatedView.questions;
};

const addOrUpdateQuestionAnalysis = async (viewId, payload) => {
  const view = await SavedView.findById(viewId).select('questionAnalysis');
  const findIndex = view.questionAnalysis.findIndex(
    (f) => f.questionIndex == payload.questionIndex
  );
  if (findIndex > -1) {
    view.questionAnalysis[findIndex] = payload;
  } else {
    view.questionAnalysis.push(payload);
  }
  await view.save();
  return view;
};

const removeAQuestionAnalysis = async (viewId, questionIndex) => {
  const view = await SavedView.findById(viewId).select('questionAnalysis');
  view.questionAnalysis = view.questionAnalysis.filter(
    (f) => f.questionIndex == questionIndex
  );
  await view.save();
  return view;
};
const deleteASavedView = async (viewId) => {
  const session = await mongoose.connection.startSession();
  logger.info('Delete saved view: Session started');
  let img = null;
  try {
    const view = await SavedView.findById(viewId);
    await session.startTransaction();
    logger.info('Delete saved view: Transection started');
    const del = await SavedView.deleteOne({ _id: viewId }).session(session);
    await AiSavedViewFilter.deleteMany({ viewId }, { session });
    await AiSavedViewSplit.deleteMany({ viewId }, { session });
    await AiSavedViewComparison.deleteMany({ viewId }, { session });
    if (view?.persona) {
      const persona = await Persona.findByIdAndDelete(view.persona).session(
        session
      );
      console.log('deleteing persona', view?.persona);
      if (persona?.img) {
        img = persona.img;
      }
    }
    await session.commitTransaction();
    logger.info('Delete saved View: Transection commited');
    for (let i = 0; i < view?.sections?.length; i++) {
      if (view?.sections?.[i]?.img) {
        const del = await deleteFile(view?.sections?.[i]?.img);
        console.log('view section image delete =>', del);
      }
    }
    if (img) {
      const del = await deleteFile(img);
      console.log('persona img deleted=>', del);
    }
    // img && (await deleteFile(img));
    const txtFilePath = path.join(configs.tempDir, 'tmp', viewId + '.txt');
    if (fs.existsSync(txtFilePath)) {
      fs.unlinkSync(txtFilePath);
    }
    return del;
  } catch (err) {
    await session.abortTransaction();
    logger.info('Delete Saved view: Transection aborted');
  } finally {
    await session.endSession();
    logger.info('Delete saved View: Transection ended');
  }
};

const SavedViewServices = {
  createSavedView,
  updateSavedView,
  getAllSavedViewOfASurvey,
  addNewSection,
  updateASection,
  deleteASection,
  addNewSubSection,
  deleteSubSection,
  updateASavedView,
  addOrUpdateQuestionAnalysis,
  removeAQuestionAnalysis,
  deleteASavedView,
  retrySaveViewAiAnalyzer,
  updateQuestionVisibility,
};

module.exports = SavedViewServices;
