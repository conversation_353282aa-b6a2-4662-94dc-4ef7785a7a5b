const { default: mongoose } = require('mongoose');
const configs = require('../../configs/configs');
const { dataPrep } = require('../AiAnalyzer/ai_analyzer.util');
const AiSavedViewComparison = require('../AiAnalyzer/ai_comparison.model');
const AiSavedViewFilter = require('../AiAnalyzer/ai_filters.model');
const AiSavedViewSplit = require('../AiAnalyzer/ai_splits.model');
const savedViewModel = require('./savedView.model');
const path = require('path');
const { logger } = require('../../../../utils/logger');
const fs = require('fs');
const Survey = require('../../../../models/Survey');

module.exports = async function jobProcessor(job) {
  if (!job.data.viewId) {
    throw new Error('View id or filepath not provided!');
  }
  await job.updateProgress(0);
  let connection = null;
  await job.log(`Started saved view ai analyzer job with id ${job.id}`);
  logger.info(
    `Saved view: Ai process started, id: ${job.data.viewId}, job id: ${job.id}`
  );

  connection
    ? null
    : (connection = await mongoose.connect(configs.mongodb_uri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }));
  await job.updateProgress(5);
  try {
    await savedViewModel.updateOne(
      {
        _id: job.data.viewId,
      },
      { ai_analyzer_status: 'processing', ai_analyzer_msg: '' }
    );
    logger.info(`Saved view: process started, id: ${job.data.viewId}`);
    const savedView = await savedViewModel.findById(job.data.viewId);

    if (!savedView) {
      // return job.moveToFailed('Saved view doesnt exists!');
      throw new Error('Saved view doesnt exists!');
    }
    const survey = await Survey.findById(savedView.survey)
      .select('templateType')
      .lean();
    //only create ai for xlsx_1 type's saved view
    const txtFilePath = path.join(
      configs.tempDir,
      'tmp',
      savedView._id + '.txt'
    );
    if (survey) {
      if (!fs.existsSync(txtFilePath)) {
        // return job.moveToFailed(
        //   'File not exists, please delete the saved view and try again!'
        // );
        throw new Error(
          'File not exists, please delete the saved view and try again!'
        );
      }
      await job.updateProgress(10);
      const array = await dataPrep(
        txtFilePath,
        savedView.survey,
        job.data.viewId
      );
      await job.updateProgress(80);

      if (savedView.viewType === 'split') {
        await AiSavedViewSplit.deleteMany({ viewId: job.data.viewId });
        await AiSavedViewSplit.insertMany(array);
      }
      if (savedView.viewType === 'filter') {
        await AiSavedViewFilter.deleteMany({ viewId: job.data.viewId });
        const filteredData = await AiSavedViewFilter.insertMany(array);
      }
      if (savedView.viewType === 'comparison') {
        await AiSavedViewComparison.deleteMany({ viewId: job.data.viewId });
        await AiSavedViewComparison.insertMany(array);
      }
      await job.updateProgress(100);
      //Update completed
      await savedViewModel.updateOne(
        {
          _id: job?.data?.viewId,
        },
        { ai_analyzer_status: 'completed', ai_analyzer_msg: '' }
      );
      const txtFilePath = path.join(
        configs.tempDir,
        'tmp',
        job?.data?.viewId + '.txt'
      );
      if (fs.existsSync(txtFilePath)) {
        fs.unlinkSync(txtFilePath);
      }
      logger.info(`Saved view: process completed, id: ${job?.data?.viewId}`);
      return true;
    } else {
      // console.log({ txtFilePath });
      //Update completed
      await savedViewModel.updateOne(
        {
          _id: job?.data?.viewId,
        },
        { ai_analyzer_status: 'completed', ai_analyzer_msg: '' }
      );
      const txtFilePath = path.join(
        configs.tempDir,
        'tmp',
        job?.data?.viewId + '.txt'
      );
      if (fs.existsSync(txtFilePath)) {
        fs.unlinkSync(txtFilePath);
      }
      logger.info(`Saved view: process completed, id: ${job?.data?.viewId}`);
      return true;
    }
    // eslint-disable-next-line no-useless-catch
  } catch (error) {
    await savedViewModel.updateOne(
      {
        _id: job?.data?.viewId,
      },
      { ai_analyzer_status: 'failed', ai_analyzer_msg: err?.message }
    );
    logger.error(`Saved view: process failed, id: ${job?.data?.viewId}`, err);
    // throw error;
  }
};
