module.exports = {
  env: {
    node: true,
    commonjs: true,
    es2021: true,
    jest: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:import/recommended',
    'plugin:prettier/recommended', // Enables prettier + disables conflicting rules
  ],
  // meta: {
  //   hasSuggestions: true,
  //   fixable: 'code', // or "whitespace"
  // },
  parserOptions: {
    ecmaVersion: 'latest',
  },
  plugins: ['import', 'security', 'unused-imports', 'sonarjs', 'prettier'],
  ignorePatterns: [
    'node_modules/',
    'dist/',
    'build/',
    '.serverless/',
    '.aws-sam/',
    '*.config.js',
    '*.configs.js',
  ],
  rules: {
    // Prettier
    'prettier/prettier': 'error',
    // General
    'no-console': 'warn',
    'no-undef': 'error',
    'no-var': 'error',
    'prefer-const': 'error',
    'prefer-template': 'warn',
    eqeqeq: 'warn',

    // Code style
    indent: ['error', 2, { SwitchCase: 1 }],
    quotes: ['error', 'single'],
    semi: ['error', 'always'],
    'semi-spacing': 'error',
    'no-mixed-spaces-and-tabs': 'warn',
    'no-multiple-empty-lines': ['error', { max: 2, maxEOF: 1 }],
    'object-property-newline': [
      'error',
      { allowAllPropertiesOnSameLine: true },
    ],
    'max-len': ['error', { code: 140 }],
    'space-infix-ops': 'error',
    'keyword-spacing': 'error',

    // Best Practices
    'no-invalid-this': 'error',
    'no-return-assign': 'error',
    'no-unused-expressions': ['error', { allowTernary: true }],
    'no-useless-concat': 'error',
    'no-useless-return': 'error',
    'no-constant-condition': 'warn',
    'no-confusing-arrow': 'error',

    // Readability
    'space-before-blocks': 'error',
    'space-in-parens': 'error',
    'space-unary-ops': 'error',
    'max-lines': ['error', { max: 500 }],
    'multiline-ternary': ['error', 'never'],
    'no-mixed-operators': 'error',
    'no-whitespace-before-property': 'error',
    'nonblock-statement-body-position': 'error',

    // Import hygiene
    'no-duplicate-imports': 'error',
    'import/order': ['warn', { groups: ['builtin', 'external', 'internal'] }],

    // Unused cleanup
    'no-unused-vars': ['warn', { argsIgnorePattern: 'req|res|next|__' }],
    'unused-imports/no-unused-imports': 'error',
    'unused-imports/no-unused-vars': [
      'error',
      {
        vars: 'all',
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_',
      },
    ],

    // ES6+
    'arrow-spacing': 'error',
    'object-shorthand': 'off',

    // SonarJS
    'sonarjs/cognitive-complexity': ['warn', 15],
    'sonarjs/no-identical-functions': 'warn',
    'sonarjs/no-identical-expressions': 'error',
  },
};
